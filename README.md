# BodyPose3DNet Modal Pipeline

This directory provides a GPU pipeline that takes **portrait** treadmill videos (1080×1920 or 2160×3840; **30 or 60 FPS only**), runs BodyPose3DNet on Modal, overlays a skeleton, and writes artifacts to S3.

## Requirements
- Modal account + GPU access
- S3 bucket and creds in env
- ONNX model in S3 at `BODYPOSE3DNET_MODEL_S3_KEY`

## Environment

```
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=...
AWS_SECRET_ACCESS_KEY=...
S3_BUCKET=maxwattz-videos
SUPABASE_URL=...
SUPABASE_SERVICE_ROLE_KEY=...
MODAL_TOKEN_ID=...
MODAL_TOKEN_SECRET=...
BODYPOSE3DNET_MODEL_S3_KEY=models/bodypose3dnet/BodyPose3DNet_deployable_accuracy_v1.0.onnx
SKELETON_JSON_PATH=modal/skeleton_34.json
INPUT_SOURCE=upload
```

## Deploy
```bash
modal deploy modal/processing_stub.py
```

## Run (local image sanity)

```bash
python -m modal.runner modal/processing_stub.py --local
```

## Web UI embedding (responsive portrait box)

Use a fixed **canvas box** with 9:16 aspect ratio, preserving proportions for overlay videos:

```html
<div class="video-box">
  <video src="/path/to/annotated.mp4" controls playsinline></video>
</div>
```

```css
.video-box { width: 100%; max-width: 420px; aspect-ratio: 9 / 16; }
.video-box video { width: 100%; height: 100%; object-fit: contain; background: #000; }
@media (min-width: 1200px) { .video-box { max-width: 540px; } }
```

- **object-fit: contain** preserves the portrait video inside its box, scaling with the page.
- For 4K (2160×3840) files the browser will downscale automatically but keep sharpness on retina displays.

## Notes

- The overlay drawing uses `modal/skeleton_34.json`. Replace this file with the **official topology** when ready.
- If the ONNX model exposes `pose2d` in crop coords, overlays will use it. If not, you'll see a placeholder bbox with a warning.
- FPS and resolution are **enforced**: videos outside {30,60} FPS or non-portrait sizes will be rejected.