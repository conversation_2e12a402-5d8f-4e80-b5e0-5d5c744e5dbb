// Modal completion webhook handler
// Called when Modal processing completes
import { supabaseAdmin } from "../common.ts";

export async function handle(req: Request): Promise<Response> {
  try {
    const body = await req.json();
    const { status, uuid, view, artifacts, error } = body;
    
    if (!uuid) {
      return new Response(JSON.stringify({ error: "Missing uuid" }), { status: 400 });
    }

    const sb = supabaseAdmin();
    const completedAt = new Date().toISOString();

    if (status === "ok" && artifacts) {
      // Success case - update with artifacts
      await Promise.all([
        // Update analysis record with completion and artifacts
        sb.from("bio_run_analysis")
          .update({
            status: "completed",
            annotated_mp4_url: artifacts.annotated_mp4,
            keypoints_json_url: artifacts.keypoints_3d_json,
            manifest_json_url: artifacts.manifest_json || null,
            completed_at: completedAt,
            view_type: view,
          })
          .eq("modal_job_id", uuid),

        // Update queue status
        sb.from("bio_modal_processing_queue")
          .update({
            status: "completed",
            completed_at: completedAt,
            modal_response: body,
          })
          .eq("modal_request_payload->job_uuid", uuid),
      ]);

      return new Response(JSON.stringify({ ok: true, message: "Analysis completed" }), { status: 200 });
      
    } else {
      // Error case - update with failure
      const errorMessage = error || "Unknown Modal processing error";
      
      await Promise.all([
        // Update analysis with error
        sb.from("bio_run_analysis")
          .update({
            status: "failed",
            error_message: errorMessage,
            completed_at: completedAt,
          })
          .eq("modal_job_id", uuid),

        // Update queue with error  
        sb.from("bio_modal_processing_queue")
          .update({
            status: "failed",
            error_message: errorMessage,
            completed_at: completedAt,
            modal_response: body,
          })
          .eq("modal_request_payload->job_uuid", uuid),
      ]);

      return new Response(JSON.stringify({ ok: true, message: "Analysis failed, status updated" }), { status: 200 });
    }
    
  } catch (e) {
    console.error("Webhook handler error:", e);
    return new Response(JSON.stringify({ error: String(e?.message ?? e) }), { status: 500 });
  }
}

export default handle;