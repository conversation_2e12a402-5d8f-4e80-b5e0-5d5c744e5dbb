// Shared helpers for edge functions
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

export const supabaseAdmin = () => {
  const url = Deno.env.get("SUPABASE_URL")!;
  const key = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;
  return createClient(url, key, { auth: { persistSession: false } });
};

export type QueuePayload = {
  video_id: string;
  view: "side" | "rear";
  s3_key: string;
  intrinsics?: { fx: number; fy: number; cx: number; cy: number };
  height_cm?: number;
};