// deno-lint-ignore-file no-explicit-any
import { supabaseAdmin, QueuePayload } from "../common.ts";

export async function handle(req: Request): Promise<Response> {
  try {
    const body = (await req.json()) as QueuePayload;
    if (!body.video_id || !body.view || !body.s3_key) {
      return new Response(JSON.stringify({ error: "Missing required fields" }), { status: 400 });
    }

    const sb = supabaseAdmin();

    // Upsert analysis holder (optional)
    const { data: analysis } = await sb
      .from("bio_run_analysis")
      .upsert({ id: body.video_id, status: "queued" }, { onConflict: "id" })
      .select()
      .single();

    const payload = {
      s3_bucket: Deno.env.get("S3_BUCKET")!,
      s3_key: body.s3_key,
      view: body.view,
      job_uuid: crypto.randomUUID(),
      intrinsics: body.intrinsics,
      height_cm: body.height_cm ?? 178,
    };

    // Insert queue row
    await sb.from("bio_modal_processing_queue").insert({
      video_id: body.video_id,
      view_type: body.view,
      status: "queued",
      modal_function_name: "process_running_video",
      modal_request_payload: payload,
      priority: 5,
    });

    // Invoke Modal function via HTTPS
    try {
      const modalResponse = await fetch(
        `https://${Deno.env.get("MODAL_USER_NAME")}--${Deno.env.get("MODAL_APP_NAME")}-run-inference.modal.run`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${Deno.env.get("MODAL_TOKEN_SECRET")}`,
          },
          body: JSON.stringify(payload),
        }
      );

      if (!modalResponse.ok) {
        throw new Error(`Modal invocation failed: ${modalResponse.status} ${await modalResponse.text()}`);
      }

      const modalResult = await modalResponse.json();
      
      // Update analysis status to processing
      await sb
        .from("bio_run_analysis") 
        .update({ status: "processing", modal_job_id: modalResult.uuid })
        .eq("id", body.video_id);

      // Update queue status
      await sb
        .from("bio_modal_processing_queue")
        .update({ 
          status: "processing", 
          started_at: new Date().toISOString(),
          modal_response: modalResult 
        })
        .eq("video_id", body.video_id);

    } catch (modalError) {
      console.error("Modal invocation failed:", modalError);
      
      // Update status to failed
      await sb
        .from("bio_run_analysis")
        .update({ status: "failed", error_message: String(modalError) })
        .eq("id", body.video_id);
        
      await sb
        .from("bio_modal_processing_queue")
        .update({ 
          status: "failed", 
          error_message: String(modalError),
          completed_at: new Date().toISOString()
        })
        .eq("video_id", body.video_id);
        
      throw modalError; // Re-throw to return 500
    }

    return new Response(JSON.stringify({ ok: true, analysis, payload }), { status: 200 });
  } catch (e) {
    return new Response(JSON.stringify({ error: String(e?.message ?? e) }), { status: 500 });
  }
}

export default handle;