# Phase Implementation Plan — ClaudeCode + Gemini (BodyPose3DNet on Modal)

> **Purpose**: Provide a **strict, step‑by‑step implementation plan** for ClaudeC<PERSON>. Gemini CLI is used to perform **parallel information gathering and file operations** (search, checksum, listing, content reads)—**not** for the runtime pose workflow. Follow these phases in order; where a phase has independent tracks, execute them **concurrently** using Gemini tasks. Do not ask questions; use the defaults below when information is missing.

**Key constraints**
- Input videos: **portrait only**, **1080×1920** or **2160×3840**, **30 or 60 FPS**. Reject others.
- Output `annotated.mp4` keeps **original portrait resolution and FPS**; to be embedded in a responsive 9:16 box in the web UI.
- Model: **NVIDIA BodyPose3DNet** (deployable‑accuracy ONNX), output **(34,3)** joints in **mm**.
- Smoothing: **One‑Euro filter** per joint/axis; single conversion **mm→m once**.
- Skeleton topology: **locked** `modal/skeleton_34.json` (v1). Allow override via `SKELETON_JSON_PATH`/`SKELETON_JSON_S3_KEY`.
- Storage: S3 bucket; artifacts to `running-metrics-{view}/{uuid}/`.
- Orchestration: Supabase Edge Function → Modal. Existing tables must be used as mapped previously.

---

## Phase 0 — Repo Bootstrap & Discovery (Gemini‑assisted, parallel)
**Goal**: Create/verify the local directory, discover authoritative model assets, and pin versions.

**Tasks**
1. **Bootstrap directory** `bodypose3dnet-pipeline/` (if missing) by copying the **Code Scaffolding** files already provided in canvas.
2. **Gemini Work Orders (run in parallel)**
   - **W0.1 — Locate official BodyPose3DNet resources**
     - Queries: "NVIDIA BodyPose3DNet ONNX", "BodyPose3DNet model card", "BodyPose3DNet GitHub".
     - Deliverables: the **official repo or model card URL(s)**, latest **ONNX file path(s)**, and **license notes**.
   - **W0.2 — Locate PeopleNet/TAO model card** (for person detection)
     - Queries: "NVIDIA PeopleNet model card NGC", "TAO PeopleNet download".
     - Deliverables: download instructions or container pull cmd. If licensing blocks automation, note fallback to HOG (already implemented) and plan for future swap‑in.
   - **W0.3 — Verify ONNX I/O**
     - Fetch the model I/O spec (input name/shape, output names/shapes). Store a snippet as `docs/bodypose3dnet_io.md`.
   - **W0.4 — Identify any sample videos** from NVIDIA or academia usable for tests (10s portrait if available). If none, skip with note.
3. **Record sources**
   - Write `docs/sources.json` with discovered URLs and model versions.

**Acceptance**
- `docs/sources.json` exists and lists BodyPose3DNet and PeopleNet references.
- `docs/bodypose3dnet_io.md` contains the input tensor name and expected output shapes.

---

## Phase 1 — GPU Image + Environment (can run while Phase 0 runs)
**Goal**: Build and validate Modal image with CUDA 11.8, onnxruntime‑gpu, OpenCV, boto3.

**Steps**
1. Build: `modal deploy modal/processing_stub.py` (or `modal build` if separate).
2. Health check (already in Dockerfile) prints available providers.
3. Ensure env vars (AWS, S3, Modal tokens, Supabase) are present.
4. Add `MODEL_CHECKSUMS.md` and record the BodyPose3DNet sha256 after download (Phase 2).

**Acceptance**
- `onnxruntime.get_available_providers()` includes `CUDAExecutionProvider`.
- Image builds without error; cold start under 5s (log timing in manifest on first run).

---

## Phase 2 — Model Acquisition & Pinning (depends on Phase 0 results)
**Goal**: Download/pin ONNX model(s), compute checksums, and wire envs.

**Steps**
1. Using URLs from **W0.1**, download BodyPose3DNet ONNX to S3 path set by `BODYPOSE3DNET_MODEL_S3_KEY`.
2. Compute `sha256` and store in `MODEL_CHECKSUMS.md` and `manifest.json` at runtime.
3. If PeopleNet binaries are accessible, cache its engine/etlt in S3 for later replacement of HOG fallback; if not, skip.
4. Update `config/pipeline.json` with `target_input: {w:256,h:192}` and portrait sizes (already present).

**Acceptance**
- `MODEL_CHECKSUMS.md` contains the model filename(s) and hashes.
- First Modal run logs `model_hash` equals the recorded hash.

---

## Phase 3 — Inference Core Wiring (strict portrait handling)
**Goal**: Ensure decode → crop (4:3) → resize 256×192 → ONNX inference → One‑Euro smoothing → **mm→m once**.

**Steps**
1. Enforce portrait + fps via `assert_portrait_and_fps` (1080×1920 or 2160×3840; FPS 30/60).
2. Per‑frame person detection (HOG fallback now) → moving average on bboxes.
3. Center crop around bbox to exact 4:3; resize to 256×192; record mapping for 2D overlays.
4. ONNXRuntime call:
   - Load with `CUDAExecutionProvider`.
   - Build input tensor from BGR frame → RGB float32 [0,1] → NCHW (1,3,256,192).
   - Locate a **(34,3)** tensor in outputs for 3D joints (mm). If present, also capture any **(34,2)** 2D keypoints (crop space).
5. One‑Euro smoothing (per joint per axis), then **exactly one** conversion mm→m. Assert this in code.

**Acceptance**
- For a 10s 1080×1920@60 clip, JSON has `≈ 600` frames, each `joints.length == 34`, all values in meters with |x|,|y|,|z| < 3.
- Manifest logs `orientation: portrait`, `fps: 60`, `width: 1080`, `height: 1920`.

---

## Phase 4 — Overlay & Artifact Generation (web‑safe)
**Goal**: Produce `annotated.mp4` at original resolution/FPS; ensure responsive display in web UI.

**Steps**
1. If 2D keypoints available, remap from crop space → original pixel coords and draw joints/edges from `modal/skeleton_34.json`. If not, draw the crop bbox with a warning watermark.
2. Use `VideoWriter` with **H.264 (`avc1`)** when available; fallback to `mp4v`.
3. Serialize `keypoints_3d.json` with timestamps (seconds), meters, and `confidence` (1.0 placeholder).
4. Write `manifest.json` with job metadata (model and skeleton hashes, timings, sizes).
5. Upload all artifacts to `s3://{bucket}/running-metrics-{view}/{uuid}/`.
6. Web UI: keep the responsive 9:16 container (CSS already in README) using `object-fit: contain`.

**Acceptance**
- Visual QA: video maintains portrait aspect with no stretching; plays in Chrome/Safari.
- JSON and MP4 sizes recorded in manifest; signed URLs load in a browser.

---

## Phase 5 — Orchestration & Supabase Integration
**Goal**: Connect upload→queue→Modal→S3→DB updates. Metrics are **out of scope** in v1.

**Steps**
1. API route `/api/init_upload_session` returns presigned POST + default intrinsics.
2. Edge Function `queue_video_job` inserts into `bio_modal_processing_queue` and starts the Modal job (webhook/polling worker—stub can log the intended call until the webhook is wired).
3. On job success, update queue row `status='completed'` and `bio_run_analysis` with `*_metrics_s3_url` (the artifact folder) and placeholders `gender='male'`, `height_inches=70`.
4. On error, set `status='error'` with `error_message` and `error_details`.

**Acceptance**
- One successful end‑to‑end run populates: queue row → completed; analysis row → artifact URLs; videos table already holds the uploaded clip.

---

## Parallelization for **Implementation** (Gemini CLI)
Use Gemini to **accelerate the build**, not the runtime. The following work orders can run in parallel:

- **G1**: (Phase 0 W0.1/W0.3) Search + capture BodyPose3DNet official doc/repo + ONNX I/O. Save `docs/sources.json`, `docs/bodypose3dnet_io.md`.
- **G2**: (Phase 0 W0.2) Search PeopleNet/TAO model card & license; export pull cmds to `docs/peoplenet_notes.md`.
- **G3**: (Phase 2) Download ONNX → upload to S3 path `BODYPOSE3DNET_MODEL_S3_KEY`; compute sha256; write `MODEL_CHECKSUMS.md`.
- **G4**: (Phase 1) Build Modal image and capture provider list; store in `docs/env_report.md`.
- **G5**: (Phase 5) Verify Supabase tables exist; dump table columns to `docs/supabase_schema_snapshot.md`.

Each Gemini task must **save artifacts** into the repo and report a short summary in `docs/task_log.md`.

---

## Deliverables Checklist (ClaudeCode must produce)
- [ ] `docs/sources.json`, `docs/bodypose3dnet_io.md`, `docs/peoplenet_notes.md`, `docs/task_log.md`
- [ ] `MODEL_CHECKSUMS.md` with sha256 for BodyPose3DNet ONNX
- [ ] Built Modal image; provider list captured
- [ ] One end‑to‑end job success with `annotated.mp4`, `keypoints_3d.json`, and `manifest.json` in S3
- [ ] Supabase rows updated as specified (queue + analysis)

---

## Non‑Negotiables Recap
- Portrait only (1080×1920 or 2160×3840); FPS 30/60 only.
- Output MP4 keeps input res/FPS; responsive 9:16 box in the web UI.
- Single mm→m conversion after One‑Euro smoothing; assert in code.
- Skeleton topology locked in repo with hash logged in manifest.

> When finished with Phases 0–5, ClaudeCode shall post (1) the commit hash, (2) the S3 artifact paths, and (3) a minimal runbook for kicking off the next video.

