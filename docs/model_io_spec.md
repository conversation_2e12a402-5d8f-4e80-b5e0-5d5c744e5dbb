# BodyPose3DNet Model I/O Specification

## Input Tensor Specification

### Primary Input
- **Name**: `input` (may vary, check with ONNX model)
- **Shape**: `[1, 3, 256, 192]`
- **Format**: NCHW (Batch, Channels, Height, Width)
- **Data Type**: float32
- **Value Range**: [0, 1] normalized RGB values
- **Preprocessing**:
  1. Resize image to 256x192 (maintaining aspect ratio with padding if needed)
  2. Convert BGR to RGB (OpenCV default is BGR)
  3. Normalize pixel values to [0, 1] range by dividing by 255.0
  4. Transpose from HWC to CHW format
  5. Add batch dimension

### Example Input Preparation (Python)
```python
# frame is a BGR image from OpenCV
frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
frame_resized = cv2.resize(frame_rgb, (192, 256))  # Note: OpenCV uses (width, height)
frame_normalized = frame_resized.astype(np.float32) / 255.0
frame_chw = np.transpose(frame_normalized, (2, 0, 1))  # HWC to CHW
input_tensor = np.expand_dims(frame_chw, axis=0)  # Add batch dimension
```

## Output Tensor Specifications

### Primary Output: 3D Joint Positions
- **Name**: `output` or `pose3d` (check model)
- **Shape**: `[1, 34, 3]`
- **Format**: (Batch, Joints, Coordinates)
- **Data Type**: float32
- **Units**: Millimeters (mm)
- **Coordinate System**: 
  - X: Horizontal (left-right)
  - Y: Vertical (up-down) 
  - Z: Depth (forward-backward)
- **Origin**: Typically centered on pelvis/root joint

### Secondary Output: 2D Joint Projections (if available)
- **Name**: `pose2d` or `proj2d` (model-specific)
- **Shape**: `[1, 34, 2]`
- **Format**: (Batch, Joints, XY)
- **Data Type**: float32
- **Units**: Pixel coordinates in 256x192 space
- **Range**: [0, 256] for Y, [0, 192] for X

### Confidence Output (if available)
- **Name**: `confidence` or `visibility`
- **Shape**: `[1, 34]` or `[1, 34, 1]`
- **Format**: Per-joint confidence scores
- **Data Type**: float32
- **Range**: [0, 1] where 1 = high confidence

## Joint Indices (34 Keypoints)

Based on standard human pose models, the 34 joints typically include:

```
0: Pelvis (root)
1-3: Spine chain
4-6: Right leg (hip, knee, ankle)
7-9: Left leg (hip, knee, ankle)
10-12: Neck and head
13-18: Right arm (shoulder, elbow, wrist, hand joints)
19-24: Left arm (shoulder, elbow, wrist, hand joints)
25-29: Right foot details
30-33: Left foot details
```

*Note: Exact mapping should be verified from model documentation or skeleton file*

## Model Constraints

1. **Fixed Input Size**: Model requires exactly 256x192 input
2. **Single Person**: Designed for single person pose estimation
3. **Full Body**: Best results when full body is visible
4. **Orientation**: Works best with upright human poses
5. **Distance**: Optimal when person occupies 60-90% of frame height

## Performance Characteristics

- **Inference Time**: ~20-30ms per frame on T4 GPU
- **Memory Usage**: ~500MB GPU memory
- **Batch Processing**: Supports batch inference but we process per-frame
- **Precision**: FP32 (can be optimized to FP16/INT8 with TensorRT)