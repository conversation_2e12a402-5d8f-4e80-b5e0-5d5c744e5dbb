# BodyPose3DNet Research Documentation

## Model Information

### BodyPose3DNet
- **Official NGC Registry Path**: `nvidia/tao/bodypose3dnet:deployable_performance_onnx_v1.0`
- **NGC Catalog URL**: https://catalog.ngc.nvidia.com/orgs/nvidia/teams/tao/models/bodypose3dnet
- **Download Command**: 
  ```bash
  ngc registry model download-version "nvidia/tao/bodypose3dnet:deployable_performance_onnx_v1.0"
  ```
- **Model Type**: ONNX format for deployment
- **Version**: v1.0 (deployable performance variant)

### Model Specifications (from NVIDIA documentation)
- **Input Shape**: (1, 3, 256, 192) - NCHW format
  - N: Batch size (1 for inference)
  - C: Channels (3 for RGB)
  - H: Height (256 pixels)
  - W: Width (192 pixels)
- **Output Shape**: (1, 34, 3) 
  - 34 keypoints/joints
  - 3D coordinates (x, y, z) in millimeters
- **Additional Output** (if available): (1, 34, 2) for 2D projections

### Keypoints Definition
The model outputs 34 3D keypoints representing human body joints. The exact mapping is defined in the model's skeleton topology file.

## PeopleNet Information

### PeopleNet (Person Detection)
- **Official NGC Registry Path**: `nvidia/tao/peoplenet:pruned_quantized_decrypted_v2.3.4`
- **NGC Catalog URL**: https://catalog.ngc.nvidia.com/orgs/nvidia/teams/tao/models/peoplenet
- **Download Command**: 
  ```bash
  ngc registry model download-version "nvidia/tao/peoplenet:pruned_quantized_decrypted_v2.3.4"
  ```
- **Model Type**: Pruned and quantized for efficiency
- **Version**: v2.3.4 (decrypted variant)

## TAO Toolkit Information

### TAO Toolkit Container
- **Container Image**: `nvcr.io/nvidia/tao/tao-toolkit:6.25.7-pyt`
- **NGC Catalog URL**: https://catalog.ngc.nvidia.com/orgs/nvidia/teams/tao/containers/tao-toolkit
- **Pull Command**:
  ```bash
  docker pull nvcr.io/nvidia/tao/tao-toolkit:6.25.7-pyt
  ```

## Licensing Requirements

Both models are provided under NVIDIA's TAO (Train, Adapt, Optimize) framework license. Key points:
- Free for development and testing
- Commercial deployment may require NVIDIA licensing
- Models are pre-trained and optimized for deployment
- Must maintain NVIDIA attribution

## Implementation Notes

1. **NGC CLI Setup Required**: 
   - Install NGC CLI: `pip install ngc-cli`
   - Configure API key from NGC account
   - Authenticate: `ngc config set`

2. **Model Download Process**:
   - Models download as compressed archives
   - Extract to find ONNX files
   - BodyPose3DNet will contain the main ONNX model
   - PeopleNet may contain TensorRT engines or ONNX variants

3. **Integration Path**:
   - For Phase 1: Use BodyPose3DNet ONNX directly
   - For Phase 2: Consider PeopleNet integration (currently using HOG fallback)

## Next Steps

1. Set up NGC CLI with authentication
2. Download BodyPose3DNet ONNX model
3. Extract and upload to S3 at configured path
4. Verify model input/output shapes match documentation