# Implementation Notes

## Key Decisions Made

### 1. Architecture Choices

- **Modal for GPU Compute**: Serverless GPU execution for cost efficiency
- **S3 for Storage**: Centralized artifact storage with presigned URLs
- **Supabase for Orchestration**: Existing database integration
- **ONNX Runtime**: Cross-platform inference without framework lock-in

### 2. Processing Pipeline

- **Per-Frame Inference**: Required by specification (no micro-batching)
- **One-Euro Filter**: Smooth temporal jitter while maintaining responsiveness
- **HOG Fallback**: Initial person detection until PeopleNet integration
- **Single mm→m Conversion**: Enforced with assertions to prevent unit drift

### 3. Video Constraints

- **Portrait Only**: 1080×1920 or 2160×3840 (9:16 aspect ratio)
- **FPS Restrictions**: 30 or 60 FPS only for consistency
- **10-Second Maximum**: Prevents memory issues and ensures quick processing
- **4:3 Crop**: Model expects 256×192 input (4:3 aspect ratio)

### 4. Coordinate Systems

- **Original Frame**: Full portrait resolution pixel coordinates
- **Model Input**: 256×192 normalized crop around person
- **3D Output**: Millimeters from model, converted to meters once
- **Overlay**: Remapped back to original frame coordinates

## Technical Considerations

### GPU Memory Management
- Model loads once per job (~500MB)
- Frames processed individually to minimize memory
- Temporary files cleaned up after upload

### Error Handling
- Portrait/FPS validation fails fast with clear errors
- S3 operations wrapped in try-catch
- Database updates atomic (all or nothing)
- Modal timeout set to 600s (10 minutes)

### Performance Optimizations
- Moving average for bbox smoothing (window=3)
- One-Euro filter tuned for 30/60 FPS
- H.264 codec preferred for web compatibility
- Parallel S3 uploads for artifacts

## Future Enhancements

### Phase 2: PeopleNet Integration
- Replace HOG with NVIDIA PeopleNet
- Better person detection accuracy
- Handle multiple people (select largest)

### Phase 3: iOS ARKit Integration
- Real camera intrinsics from device
- LiDAR depth data integration
- Live preview capabilities

### Phase 4: Metrics Pipeline
- Cadence calculation from ankle positions
- Ground contact time analysis
- Stride length and symmetry
- Vertical oscillation tracking

## Debugging Tips

1. **Model Loading Issues**: Check S3 permissions and model path
2. **Video Rejection**: Verify portrait orientation and FPS
3. **Overlay Misalignment**: Check coordinate remapping math
4. **GPU Errors**: Ensure CUDA provider available
5. **S3 Timeouts**: Check AWS credentials and network

## Environment Variables Reference

Critical variables that must be set:
- `MODAL_TOKEN_ID` & `MODAL_TOKEN_SECRET`
- `AWS_ACCESS_KEY_ID` & `AWS_SECRET_ACCESS_KEY`
- `BODYPOSE3DNET_MODEL_S3_KEY`
- `S3_BUCKET`
- `NVIDIA_API_KEY` (for NGC downloads)