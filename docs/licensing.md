# Licensing Information

## NVIDIA TAO License

Both BodyPose3DNet and PeopleNet are provided under the NVIDIA TAO (Train, Adapt, Optimize) License Agreement.

### Key Points:

1. **Development & Testing**: Free to use for development, testing, and evaluation purposes
2. **Commercial Deployment**: May require additional licensing from NVIDIA for production use
3. **Attribution**: Must maintain NVIDIA copyright notices and attributions
4. **Redistribution**: Model files should not be redistributed; users should download from NGC
5. **Modifications**: Models can be fine-tuned but derivative works fall under same license

### License Types:

- **Research License**: For academic and non-commercial research
- **Commercial License**: Required for production deployment in commercial applications
- **Enterprise License**: For large-scale deployments

### Compliance Steps:

1. Register for NGC account at https://ngc.nvidia.com
2. Accept TAO License Agreement when downloading models
3. For commercial use, contact NVIDIA for licensing options
4. Maintain audit trail of model downloads and deployments

## Third-Party Licenses

### Modal
- MIT License
- Free for development with usage-based pricing for compute

### OpenCV
- Apache 2.0 License
- Free for commercial use

### ONNX Runtime
- MIT License
- Free for commercial use

### Other Dependencies
All Python packages in requirements.txt are under permissive licenses (MIT, Apache 2.0, or BSD).

## Recommendations

1. For development: Current setup is fully compliant
2. For production: Contact NVIDIA sales for commercial TAO license
3. Keep records of all model versions and download sources
4. Review licenses before adding new dependencies