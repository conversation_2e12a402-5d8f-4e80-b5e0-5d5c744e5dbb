-- BodyPose3DNet Modal Pipeline Database Schema
-- Based on edge function requirements and processing pipeline

-- Bio run analysis table - tracks video analysis jobs
CREATE TABLE IF NOT EXISTS bio_run_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  status TEXT NOT NULL CHECK (status IN ('queued', 'processing', 'completed', 'failed')) DEFAULT 'queued',
  modal_job_id UUID,
  annotated_mp4_url TEXT,
  keypoints_json_url TEXT,
  manifest_json_url TEXT,
  view_type TEXT CHECK (view_type IN ('side', 'rear')),
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  
  -- Indexes for common queries
  INDEX idx_bio_run_analysis_status ON bio_run_analysis(status),
  INDEX idx_bio_run_analysis_modal_job_id ON bio_run_analysis(modal_job_id)
);

-- Modal processing queue - tracks queue entries and processing status  
CREATE TABLE IF NOT EXISTS bio_modal_processing_queue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  video_id UUID NOT NULL REFERENCES bio_run_analysis(id),
  view_type TEXT NOT NULL CHECK (view_type IN ('side', 'rear')),
  status TEXT NOT NULL CHECK (status IN ('queued', 'processing', 'completed', 'failed')) DEFAULT 'queued',
  modal_function_name TEXT NOT NULL DEFAULT 'process_running_video',
  modal_request_payload JSONB NOT NULL,
  modal_response JSONB,
  priority INTEGER DEFAULT 5,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  
  -- Indexes for queue processing
  INDEX idx_queue_status_priority ON bio_modal_processing_queue(status, priority, created_at),
  INDEX idx_queue_video_id ON bio_modal_processing_queue(video_id)
);

-- Row Level Security (RLS) policies
ALTER TABLE bio_run_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE bio_modal_processing_queue ENABLE ROW LEVEL SECURITY;

-- Service role can do everything
CREATE POLICY "Service role full access on bio_run_analysis" ON bio_run_analysis
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role full access on bio_modal_processing_queue" ON bio_modal_processing_queue  
  FOR ALL USING (auth.role() = 'service_role');

-- Anonymous users can only read completed analysis results
CREATE POLICY "Public read completed analysis" ON bio_run_analysis
  FOR SELECT USING (status = 'completed' AND auth.role() = 'anon');

-- Authenticated users can access their own records (if user_id column exists)
-- CREATE POLICY "Users access own analysis" ON bio_run_analysis
--   FOR ALL USING (auth.uid() = user_id);

-- Performance optimization: ensure JSONB fields are indexed for queries  
CREATE INDEX IF NOT EXISTS idx_modal_payload_job_uuid 
ON bio_modal_processing_queue USING GIN ((modal_request_payload->'job_uuid'));

CREATE INDEX IF NOT EXISTS idx_modal_payload_s3_key
ON bio_modal_processing_queue USING GIN ((modal_request_payload->'s3_key'));