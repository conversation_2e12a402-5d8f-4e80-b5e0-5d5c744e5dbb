# ClaudeCode Master Prompt — BodyPose3DNet Modal Pipeline

> **Objective:** Implement a GPU pipeline that takes an uploaded treadmill video (side or rear), runs NVIDIA BodyPose3DNet inference on Modal, smooths and overlays a skeleton, and writes three artifacts to S3: `annotated.mp4`, `keypoints_3d.json` (meters), and `manifest.json`. Update Supabase records to reflect job status and artifact URLs. This project is a **standalone directory** that will later be mounted into the Max Wattz monorepo and the iOS app. **Do not ask questions. If a value is unknown, use the specified defaults and stubs below.**

---

## System role & style

- You are an implementation agent. Use **deterministic** commands, explicit versions, and safe defaults. No speculation.
- Make the system **idempotent**: every command can be run twice without side effects.
- Parallelize independent steps using **Gemini CLI** where indicated (fan-out fan-in pattern). Avoid unnecessary token usage.

---

## Tech decisions (non-negotiable)

- **Model**: BodyPose3DNet deployable accuracy ONNX (34 joints; outputs mm). Shape: `(1,34,3)`.
- **GPU runtime**: ONNXRuntime-GPU in a Modal function with CUDA 11.8 base image.
- **Pre-detection**: NVIDIA PeopleNet for per-frame person bbox (1 subject). Jitter smoothing allowed.
- **Smoothing**: One-Euro filter per joint, per axis; convert mm→m **once** post-smoothing.
- **Input**: Phase 1 = **upload mode** (web app). Phase 2 will add **iOS ARKit mode**. Use the **Input Adapter** interface now so we don't have to comment code later.
- **Storage**: S3 bucket already exists (name supplied via env). Artifacts saved under `running-metrics-{view}/{uuid}/`.
- **Orchestration**: Supabase Edge Function enqueues work in `bio_modal_processing_queue` and triggers Modal.
- **DB**: Use existing Supabase tables (see mapping below). We do **not** create new tables.

---

## ENV & secrets (read-only; treat as present)

- `AWS_REGION`, `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`
- `S3_BUCKET=maxwattz-videos`
- `SUPABASE_URL`, `SUPABASE_SERVICE_ROLE_KEY`
- `MODAL_TOKEN_ID`, `MODAL_TOKEN_SECRET`
- `BODYPOSE3DNET_MODEL_S3_KEY` (path to ONNX)
- `INPUT_SOURCE` ("upload" | "ios", default "upload")

Do **not** print secrets. Fail fast if any are missing.

---

## Directory scaffold (create exactly)

```
bodypose3dnet-pipeline/
├─ modal/
│  ├─ Dockerfile
│  ├─ requirements.txt
│  ├─ processing_stub.py            # Modal entrypoint, run_inference()
│  ├─ one_euro.py                   # One-Euro filter impl
│  ├─ skeleton_34.json              # Locked topology (v1); allow override via env
│  └─ utils.py
├─ adapters/
│  ├─ base_source.py                # interface: get_frames(), get_intrinsics(), get_metadata()
│  ├─ upload_source.py              # reads from S3, returns frames & defaults
│  └─ ios_source.py                 # stub now; later ARKit
├─ edge-functions/
│  ├─ queue_video_job/index.ts      # Supabase Edge Function (TypeScript)
│  └─ common.ts
├─ api/
│  └─ init_upload_session.ts        # returns presigned URLs + session token
├─ supabase/
│  └─ sql/permissions.sql           # RLS tweaks if needed (read-only for metrics URLs)
├─ config/
│  ├─ logging.yaml
│  └─ pipeline.json                 # tunables (bbox_jitter_px, fps limits, etc.)
├─ tests/
│  ├─ unit/
│  │  ├─ test_one_euro.py
│  │  └─ test_coordinate_ledger.py
│  └─ e2e/
│     └─ test_sample_video.py
├─ scripts/
│  ├─ make_manifest.py
│  └─ local_ffprobe.sh
└─ README.md
```

---

## Input Adapter interface (no commenting toggles later)

Create an abstract class `BaseSource`:

```python
class BaseSource:
    def get_frames(self) -> Iterable[np.ndarray]: ...          # BGR frames, original res
    def get_intrinsics(self) -> dict: ...                      # {fx,fy,cx,cy} floats
    def get_metadata(self) -> dict: ...                        # {fps,width,height,codec,duration}
```

- **UploadSource**: accepts `{bucket, key}`; reads with boto3; decodes with OpenCV; intrinsics: if missing, return default `{fx:1190.0, fy:1190.0, cx:width/2, cy:height/2}`.
- **IosSource (stub)**: returns `NotImplementedError` for now.
- `INPUT_SOURCE` env selects adapter. Default is UploadSource.

---

## Coordinate Ledger (strict)

Maintain a dict per frame:

```json
{
  "frame_index": 42,
  "orig_res": {"w":1920,"h":1080},
  "crop_res": {"w":256,"h":192},
  "bbox_px": [x,y,w,h],
  "pose3d_mm": [[x,y,z]*34],
  "pose3d_m": [[x,y,z]*34],
  "ts": 0.700
}
```

- Only one mm→m conversion allowed. Add an assertion to enforce this. Persist end-of-job `manifest.json` with checksums and timing.

---

## Modal implementation (processing\_stub.py)

**Contract**: `run_inference(payload)` returns `{status:"ok", uuid, view, artifacts:{mp4,json,manifest}}` or `{status:"error", message}`.

**Steps (in order; parallelize where safe):**

1. Download input from S3 (UploadSource) → stream decode.
2. PeopleNet detection per frame → bbox smoothing (moving average window=3).
3. Center-crop to 4:3 → resize to 256×192 (record mapping).
4. ONNXRuntime-GPU per-frame inference (output mm).
5. One-Euro smoothing → convert to meters (exactly once).
6. Draw overlay back on original frames; write `annotated.mp4` with original FPS & resolution.
7. Serialize `keypoints_3d.json` with timestamps (meters).
8. Build `manifest.json` (fps, frames, timings, commit SHA, model hash).
9. Upload all artifacts to `s3://$S3_BUCKET/running-metrics-{view}/{uuid}/`.

**Parallelization (Gemini CLI):**

- Fan-out workers: (A) decode+detect, (B) crop+resize, (C) onnx+smooth, (D) overlay+encode. Use queues to hand off frames by index. Ensure final VideoWriter receives frames in-order.

**Failure policy:** Raise and capture exceptions into `error_message` + `error_details` (json) and return `{status:"error"...}`. Never partial-writes.

---

## Edge Function: `queue_video_job/index.ts`

**Trigger:** called by web app after an upload completes for a given **view** and video UUID.

**Inputs**

```ts
{ video_id: string, view: 'side'|'rear', s3_key: string, intrinsics?: {fx:number,fy:number,cx:number,cy:number}, height_cm?: number }
```

**Actions**

1. Insert into `bio_modal_processing_queue` with `status='queued'`, `video_id`, `view_type`, `modal_function_name='process_running_video'`, `modal_request_payload` (payload JSON), `priority=5` default.
2. If no `bio_run_analysis` row exists for this pair, create one and link via `analysis_id` (store gender='male', height\_inches=70 until profile integration).
3. Call Modal to start the job; immediately update queue row with `modal_job_id`, `started_at`.

**On Modal callback (polling or webhook)**

- On success: update queue row `status='completed'`, `frames_processed`, `gpu_time_seconds`, `total_time_seconds`, `completed_at`. Update `bio_run_analysis` with `side_video_id` or `rear_video_id` and `*_metrics_s3_url` (we’re using the `running-metrics-*` path) and set `model_version`, `smoothnet_version` placeholders.
- On error: set `status='error'`, store `error_message` and `error_details` JSON.

---

## Supabase table mapping (use existing schema)

- **bio\_run\_videos**: record per uploaded file (bitrate, codec, fps, width, height, mime\_type, original\_filename, s3\_bucket, s3\_key, s3\_url, duration\_seconds, is\_valid, validation\_errors, user\_email, view\_type). Primary key: `id` (uuid).
- **bio\_run\_upload\_sessions**: tracks side+rear session-level state (session\_token, side/rear uploaded flags + upload ids). Use when offering presigned URLs.
- **bio\_modal\_processing\_queue**: one row per processing job with `video_id` FK, `view_type`, `status`, timings, modal ids, request/response payloads.
- **bio\_run\_analysis**: roll-up record keyed by `id` (uuid), holding `side_video_id`, `rear_video_id`, `side_metrics_s3_url`, `rear_metrics_s3_url`, `status`, `model_version`, `processing_completed_at`, `processing_duration_seconds`, `user_email`, `gender`, `height_inches`, plus summary fields (cadence\_avg, stride\_length\_avg, etc.) to be filled later. For v1, write only the URLs, counts, and status.
- **bio\_run\_analysis\_metrics**: **do not write in v1** (reserved for future or will be replaced by QuestDB). Leave empty.

---

## API route: `api/init_upload_session.ts`

- Creates `bio_run_upload_sessions` row with a new `session_token` and returns **two presigned URLs** (side, rear) and an `analysis_id`.
- Also returns default intrinsics placeholder `{fx,fy,cx,cy}` for debugging overlays if app wants to pass them through.

---

## File paths & naming

- Input videos: `uploads/{uuid}/original.mp4`
- Outputs: `running-metrics-{view}/{uuid}/annotated.mp4`, `keypoints_3d.json`, `manifest.json`
- Use UUIDs everywhere and propagate into `bio_run_videos.id` and queue rows.

---

## Drawing spec (overlay)

- Keep original FPS and resolution.
- Use consistent joint colors; thicker lines for major limbs; circles for joints (radius 4–6 px at 1080p).
- Left vs Right coloring (e.g., blue vs cyan) stays stable across frames.

---

## Validation & tests

- **Unit**: One-Euro filter produces monotonic smoothing for a noisy sinusoid; coordinate-ledger test asserts exactly one mm→m conversion.
- **E2E**: For a 10s clip @60 FPS: outputs exist, FPS preserved, JSON length == frame\_count, and values are in meters with |x|,|y|,|z| < 3.
- **DB**: After success, queue `status='completed'`, analysis row has `*_metrics_s3_url` set, and `*_video_id` populated.

---

## Commands (document in README)

```bash
# Build Modal image
modal deploy modal/processing_stub.py

# Local quick test (no GPU):
python -m modal.runner modal/processing_stub.py --local --s3-key uploads/DEMO/original.mp4 --view side

# Run Edge Function locally (supabase CLI)
supabase functions serve queue_video_job
```

---

## Non-functional requirements

- Total pipeline per 10s clip < 20s on a T4-class GPU.
- Deterministic outputs for identical inputs (same model hash + skeleton file hash).
- All logs include `job_uuid` and `video_id` correlation ids.

---

## Defaults to use until iOS integration

- `gender='male'`, `height_inches=70` (5'10").
- Intrinsics default if missing: `{fx:1190, fy:1190, cx:W/2, cy:H/2}`.

---

## Deliverables

- All source files above, runnable with our existing env vars.
- A short OPERATIONS.md with the runbook (queue a job, check status, find artifacts).
- No UI work beyond a simple upload form stub and a status JSON page.

> When finished, present a summary of files created and the exact commands to deploy the Modal function and the Supabase edge function.

