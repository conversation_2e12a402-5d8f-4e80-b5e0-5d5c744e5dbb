BodyPose3DNet Modal Pipeline - Implementation PRD

  Executive Summary

  Build a GPU-accelerated pipeline on Modal that processes portrait treadmill videos (side/rear views) through NVIDIA BodyPose3DNet to produce smooth skeleton overlays and 3D joint tracking data. The system
  integrates with existing MaxWattz infrastructure via S3 storage and Supabase orchestration.

  Core Requirements

  - Input: Portrait videos only (1080×1920 or 2160×3840) at 30/60 FPS
  - Model: NVIDIA BodyPose3DNet ONNX (34 joints, outputs in mm)
  - Output: 3 artifacts per video - annotated.mp4, keypoints_3d.json, manifest.json
  - Storage: S3 bucket maxwattz-videos under running-metrics-{view}/{uuid}/
  - Processing: One-Euro smoothing, single mm→m conversion
  - Performance: <20s total for 10s clip on T4 GPU

  ---
  Phase 0: Project Bootstrap & Discovery ✅ COMPLETED

  Task 0.1: Create Project Structure ✅ COMPLETED

  Steps:
  1. ✅ Create bodypose3dnet-pipeline/ directory structure
  2. ✅ Copy scaffolding files from provided markdown  
  3. ✅ Initialize git repository
  4. ✅ Create .env from .env.local values
  5. ✅ Set up .gitignore (exclude .env, *.pyc, pycache, etc.)

  Deliverables:
  - ✅ Complete directory structure matching scaffold
  - ✅ Git repository initialized
  - ✅ Environment variables configured

  Task 0.2: Gemini Parallel Discovery ✅ COMPLETED

  Research Results:
  - ✅ BodyPose3DNet NGC path: nvidia/tao/bodypose3dnet:deployable_performance_onnx_v1.0
  - ✅ PeopleNet NGC path: nvidia/tao/peoplenet:pruned_quantized_decrypted_v2.3.4
  - ✅ Model I/O specs: Input [1,3,256,192], Output [1,34,3] in mm

  Task 0.3: Document Findings ✅ COMPLETED

  Created Files:
  - ✅ docs/sources.json - Complete model registry with NGC paths
  - ✅ All documentation files created
  - ✅ Model download URLs identified  
  - ✅ Input/output tensor shapes documented
  - ✅ Licensing requirements noted

  ---
  Phase 1: Modal Environment Setup ✅ COMPLETED

  Task 1.1: Configure Modal Authentication ✅ COMPLETED

  Steps:
  1. ✅ Install modal: pip install modal (resolved version conflicts)
  2. ✅ Authenticate: modal token set --token-id $MODAL_TOKEN_ID --token-secret $MODAL_TOKEN_SECRET
  3. ✅ Verify connection: modal token validate
  4. ✅ Create workspace accessible

  Deliverables:
  - ✅ Modal CLI authenticated
  - ✅ Workspace accessible

  Task 1.2: Build GPU Docker Image ✅ COMPLETED

  Steps:
  1. ✅ Create modal/Dockerfile with CUDA 11.8 base
  2. ✅ Add all Python dependencies to modal/requirements.txt
  3. ✅ Implement health check verifying ONNX providers
  4. ✅ Successfully build GPU Docker image
  5. ✅ Resolved all syntax/dependency issues

  Acceptance Criteria:
  - ✅ Dockerfile builds without errors
  - ✅ Health check confirms CUDA and TensorRT providers available
  - ✅ All dependencies resolve

  Task 1.3: Deploy Modal Stub ✅ COMPLETED

  Steps:
  1. ✅ Create modal/processing_stub.py with basic structure
  2. ✅ Add GPU configuration and timeout settings  
  3. ✅ Deploy: modal deploy modal/processing_stub.py (fixed API changes)
  4. ✅ Verify deployment successful
  5. ✅ GPU function confirmed working

  Deliverables:
  - ✅ Modal function deployed successfully
  - ✅ GPU environment verified with CUDA providers
  - ✅ Core pipeline structure in place

  Key Fixes Applied:
  - Changed modal.Stub → modal.App (API update)
  - Fixed import paths for local modules
  - Resolved package version conflicts
  - Confirmed GPU Docker image builds successfully

  ---
  Phase 2: Model Acquisition & Configuration ✅ COMPLETED

  Task 2.1: Download BodyPose3DNet Model ✅ COMPLETED

  Steps:
  1. ✅ Setup NGC CLI with NVIDIA API key (used direct API instead)
  2. ✅ Downloaded bodypose3dnet_accuracy.onnx (70.6 MB) 
  3. ✅ Verify model size and format (70,575,749 bytes, valid ONNX)
  4. ✅ Compute SHA256 checksum: 0452b785a70fcd6bc5bd4069249bdfd85eb139c9e9216bcf81f89df33945d028
  5. ✅ Upload to Modal Volume (bodypose3d-models) instead of S3 for optimal performance
  6. ✅ Create MODEL_CHECKSUMS.md with hash

  Deliverables:
  - ✅ Model uploaded to Modal Volume (faster than S3)
  - ✅ Checksum documented in MODEL_CHECKSUMS.md
  - ✅ Modal Volume verified and accessible

  Key Technical Decision:
  - Used Modal Volume storage instead of S3 for model
  - Benefits: No download latency, instant access, production-ready
  - Volume name: bodypose3d-models
  - Model path in container: /models/bodypose3dnet_accuracy.onnx

  Task 2.2: Configure Pipeline Settings ✅ COMPLETED

  Steps:
  1. ✅ config/pipeline.json already configured with processing parameters
  2. ✅ modal/skeleton_34.json verified with official 34-joint topology
  3. ✅ Environment variables updated for Modal Volume (not S3)
  4. ✅ config/logging.yaml already created for structured logging
  5. ✅ Configuration documented in code and MODEL_CHECKSUMS.md

  Acceptance Criteria:
  - ✅ All config files created and verified
  - ✅ Model path accessible from Modal Volume (/models/)
  - ✅ Skeleton topology locked and versioned

  Task 2.3: Implement Model Loading ✅ COMPLETED

  Steps:
  1. ✅ Updated to use Modal Volume (no S3 download needed)
  2. ✅ Model caching automatic with Modal Volume mounting
  3. ✅ ONNX session initialization with CUDA provider implemented
  4. ✅ Model loading verified with proper input/output tensors
  5. ✅ Timing instrumentation added in processing_stub.py

  Deliverables:
  - ✅ Model loads instantly from Modal Volume
  - ✅ CUDA provider active and verified
  - ✅ Load time <1s (no download needed)
  
  Key Updates to processing_stub.py:
  - Added Modal Volume mounting: volumes={"/models": model_volume}
  - Fixed BodyPose3DNet multi-input handling (5 input tensors)
  - Fixed output parsing for 4 output tensors
  - Added camera intrinsics and auxiliary tensor preparation

  ---
  Phase 3: Core Processing Pipeline (4-5 hours)

  Task 3.1: Video Input Processing

  Steps:
  1. Implement adapters/base_source.py interface
  2. Create adapters/upload_source.py with S3 download
  3. Add portrait validation (1080×1920 or 2160×3840)
  4. Enforce FPS constraints (30 or 60 only)
  5. Implement frame decoding with OpenCV

  Acceptance Criteria:
  - Rejects non-portrait videos
  - Validates FPS requirements
  - Efficiently decodes frames

  Task 3.2: Person Detection & Tracking

  Steps:
  1. Implement HOG detector fallback (PeopleNet later)
  2. Add per-frame detection logic
  3. Implement moving average smoothing (window=3)
  4. Calculate and validate jitter metrics
  5. Log bbox stability stats

  Deliverables:
  - Person detected each frame
  - Bbox jitter <2px median
  - Smooth tracking maintained

  Task 3.3: Frame Preprocessing

  Steps:
  1. Implement center-crop to 4:3 aspect ratio
  2. Add resize to 256×192 (model input size)
  3. Record crop→original mapping for overlays
  4. Convert BGR→RGB and normalize to [0,1]
  5. Format as NCHW tensor (1,3,256,192)

  Acceptance Criteria:
  - Maintains aspect ratio (no distortion)
  - Mapping allows accurate overlay
  - Tensor format matches model needs

  Task 3.4: ONNX Inference Implementation

  Steps:
  1. Create inference session with CUDA provider
  2. Run model per-frame (no batching)
  3. Extract (34,3) joint positions in mm
  4. Optionally extract (34,2) 2D projections
  5. Add timing instrumentation per frame

  Deliverables:
  - Inference <30ms per frame
  - 34 joints detected
  - Output in millimeters

  Task 3.5: Smoothing & Coordinate Conversion

  Steps:
  1. Implement One-Euro filter (modal/one_euro.py)
  2. Create filter bank: 34 joints × 3 axes
  3. Apply smoothing with fps-based frequency
  4. Convert mm→m exactly once (with assertion)
  5. Validate output ranges (|x|,|y|,|z| < 3.0m)

  Acceptance Criteria:
  - Smooth motion without lag
  - Single unit conversion verified
  - Reasonable position values

  ---
  Phase 4: Output Generation (3-4 hours)

  Task 4.1: Skeleton Overlay Rendering

  Steps:
  1. Load skeleton topology from skeleton_34.json
  2. Remap 2D joints from crop→original coordinates
  3. Draw joints as circles (radius 4-6px)
  4. Draw edges with consistent left/right colors
  5. Add fallback bbox if no 2D available

  Deliverables:
  - Clean skeleton overlay
  - Original resolution maintained
  - Consistent coloring

  Task 4.2: Video Encoding

  Steps:
  1. Initialize VideoWriter with H.264 codec (prefer 'avc1')
  2. Set original FPS and resolution (portrait)
  3. Write frames sequentially with overlays
  4. Ensure web-compatible format
  5. Verify no aspect ratio changes

  Acceptance Criteria:
  - Output matches input resolution/FPS
  - Plays in Chrome/Safari
  - File size reasonable

  Task 4.3: JSON Data Serialization

  Steps:
  1. Structure keypoints_3d.json per schema
  2. Include timestamp (seconds) per frame
  3. Add joint positions in meters
  4. Include confidence scores (1.0 default)
  5. Validate JSON structure

  Deliverables:
  - Valid JSON with all frames
  - Timestamps = frame_index/fps
  - Values in meters

  Task 4.4: Manifest Generation

  Steps:
  1. Create comprehensive manifest.json
  2. Include model & skeleton checksums
  3. Add timing measurements
  4. Record input/output metadata
  5. Include job configuration

  Acceptance Criteria:
  - Complete job documentation
  - Reproducible configuration
  - Performance metrics logged

  Task 4.5: S3 Upload

  Steps:
  1. Create UUID-based paths
  2. Upload annotated.mp4
  3. Upload keypoints_3d.json
  4. Upload manifest.json
  5. Verify all uploads successful

  Deliverables:
  - All artifacts in S3
  - Correct path structure
  - Files accessible

  ---
  Phase 5: Integration & Orchestration (3-4 hours)

  Task 5.1: API Endpoint Setup

  Steps:
  1. Create api/init_upload_session.ts
  2. Generate presigned S3 POST URLs
  3. Return default intrinsics
  4. Create session UUID
  5. Test with curl/Postman

  Acceptance Criteria:
  - Presigned URLs work
  - Session tracking enabled
  - CORS configured

  Task 5.2: Supabase Edge Function

  Steps:
  1. Create edge-functions/queue_video_job/index.ts
  2. Implement queue insertion logic
  3. Add Modal job triggering (stub initially)
  4. Handle error cases
  5. Deploy to Supabase

  Deliverables:
  - Edge function deployed
  - Queue row created
  - Modal payload formatted

  Task 5.3: Database Integration

  Steps:
  1. Verify Supabase table schemas
  2. Implement queue status updates
  3. Add analysis record creation
  4. Update artifact URLs on completion
  5. Handle error states properly

  Acceptance Criteria:
  - Queue row tracks job lifecycle
  - Analysis record has S3 URLs
  - Error states captured

  Task 5.4: End-to-End Testing

  Steps:
  1. Upload test video via API
  2. Verify queue entry created
  3. Run Modal processing
  4. Check S3 artifacts
  5. Validate database updates

  Deliverables:
  - Complete pipeline working
  - All artifacts generated
  - Database consistent

  Task 5.5: Web UI Integration

  Steps:
  1. Add responsive 9:16 video container CSS
  2. Implement artifact display component
  3. Add status polling/updates
  4. Test on mobile viewport
  5. Document embed approach

  Acceptance Criteria:
  - Video displays correctly
  - No aspect ratio distortion
  - Mobile responsive

  ---
  Phase 6: Testing & Documentation (2-3 hours)

  Task 6.1: Unit Tests

  Steps:
  1. Test One-Euro filter convergence
  2. Test coordinate conversion (mm→m once)
  3. Test bbox smoothing algorithm
  4. Test crop/resize mappings
  5. Add to CI pipeline

  Deliverables:
  - All unit tests passing
  - Key invariants verified
  - CI integration ready

  Task 6.2: Integration Tests

  Steps:
  1. Test portrait video validation
  2. Test S3 upload/download
  3. Test Modal deployment
  4. Test database operations
  5. Create test fixtures

  Acceptance Criteria:
  - All components tested
  - Error cases handled
  - Fixtures documented

  Task 6.3: Documentation

  Steps:
  1. Update README with setup instructions
  2. Create OPERATIONS.md runbook
  3. Document environment variables
  4. Add architecture diagrams
  5. Include example commands

  Deliverables:
  - Complete documentation
  - Runbook for operations
  - Clear setup guide

  ---
  ## IMPLEMENTATION STATUS SUMMARY

  ### ✅ COMPLETED PHASES
  - **Phase 0**: Project Bootstrap & Discovery (100%)
    - All directory structure, git repo, documentation created
    - NGC model paths identified and documented
    - Environment variables configured
  
  - **Phase 1**: Modal Environment Setup (100%)
    - Modal authentication successful
    - GPU Docker image builds with CUDA providers
    - Modal deployment working (API changes resolved)

  - **Phase 2**: Model Acquisition & Configuration (100%)
    - All tasks completed successfully
    - Model stored in Modal Volume for optimal performance
    - Processing stub updated with proper multi-tensor handling

  ### ✅ COMPLETED PHASES  
  - **Phase 0**: Project Bootstrap & Discovery (100%)
  - **Phase 1**: Modal Environment Setup (100%)
  - **Phase 2**: Model Acquisition & Configuration (100%) 
  - **Phase 3**: Core Processing Pipeline (100%) ✅ **JUST COMPLETED**
    - ✅ Task 3.1: Video Input Processing Enhancements (adapter interface)
    - ✅ Task 3.2: Person Detection & Tracking Improvements (stability metrics)
    - ✅ Task 3.3: Frame Preprocessing Validation (aspect ratio, tensor validation)
    - ✅ Task 3.4: ONNX Inference Optimization (multi-tensor, timing)
    - ✅ Task 3.5: Smoothing & Coordinate Validation (anatomical constraints)

  ### ✅ **Phase 4**: Output Generation (100%) ✅ **COMPLETE**
    - ✅ Task 4.1: Skeleton Overlay Rendering (joints, edges, fallback bbox)
    - ✅ Task 4.2: Video Encoding (H.264, original FPS/resolution)  
    - ✅ Task 4.3: JSON Data Serialization (timestamps, meters, confidence)
    - ✅ Task 4.4: Manifest Generation (checksums, timing, metadata)
    - ✅ Task 4.5: S3 Upload (all 3 artifacts with UUID paths)

  ### ✅ **Phase 5**: Integration & Orchestration (100%) ✅ **COMPLETE**
    - ✅ Task 5.1: API Endpoint Setup (presigned URLs, intrinsics)
    - ✅ Task 5.2: Modal HTTP invocation from Supabase Edge Functions
    - ✅ Task 5.2b: Modal completion webhook handler with error handling
    - ✅ Task 5.3: Database Integration (schema, RLS policies, JSONB indexes)
    - ✅ Task 5.4: E2E Testing with Michael_test_2.MOV (complete pipeline)
    - ✅ Task 5.5: Web UI Integration (React components, artifact display)

  ### 🔄 CURRENT PHASE STATUS
  **Ready for Phase 6**: Testing & Documentation

  ### 📋 REMAINING PHASES
  - **Phase 6**: Testing & Documentation (unit tests, documentation, runbooks)

  ### 🛠️ KEY TECHNICAL ACHIEVEMENTS
  - GPU environment verified with CUDA and TensorRT providers
  - Complete project structure matching specifications
  - All core modules implemented (utils.py, one_euro.py, processing_stub.py)
  - **BodyPose3DNet model integrated** with Modal Volume storage
  - **Proper multi-tensor inference** (5 inputs, 4 outputs) implemented
  - **Production-optimized** model loading (no S3 download latency)
  - **Complete model verification** (70.6MB, valid ONNX, documented checksums)
  - Supabase integration scaffolded
  - Edge functions structure ready

  ---
  Deliverables Summary

  Required Artifacts

  1. Code: Complete bodypose3dnet-pipeline/ directory ✅
  2. Model: ONNX file in Modal Volume with checksum ✅ COMPLETED
  3. Deployment: Modal function live and callable ✅
  4. Database: Supabase tables integrated 📋 PENDING
  5. Documentation: README, OPERATIONS.md, API docs 📋 PENDING

  Success Metrics

  - 10s portrait video → 3 artifacts in <20s
  - Skeleton overlay at original resolution/FPS
  - JSON data in meters with proper timestamps
  - Database records tracking complete pipeline
  - No memory leaks or GPU OOM errors

  Post-Implementation

  - Commit hash of final code
  - S3 paths for test artifacts
  - Modal function endpoint
  - Monitoring dashboard URL
  - Next steps for iOS integration