from __future__ import annotations
import cv2
from typing import Iterable, Dict
import numpy as np
from utils import assert_portrait_and_fps
from .base_source import BaseSource, VideoValidationError

class UploadSource(BaseSource):
    def __init__(self, local_video_path: str, default_intrinsics=None):
        super().__init__(source_id=local_video_path)
        self.path = local_video_path
        self.default_intrinsics = default_intrinsics or {}

    def get_frames(self) -> Iterable[np.ndarray]:
        # Validate requirements first
        if not self.validate_requirements():
            raise VideoValidationError(f"Video validation failed: {self.path}")
            
        cap = cv2.VideoCapture(self.path)
        try:
            metadata = self.get_metadata()
            fps = metadata["fps"]
            max_frames = fps * 10
            i = 0
            while i < max_frames:
                ok, fr = cap.read()
                if not ok:
                    break
                yield fr
                i += 1
        finally:
            cap.release()

    def get_intrinsics(self) -> Dict[str, float]:
        # Defaults for debug; iOS will provide real values later
        metadata = self.get_metadata()  # Uses cached metadata
        W, H = metadata["width"], metadata["height"]
        fx = float(self.default_intrinsics.get("fx", 1190.0))
        fy = float(self.default_intrinsics.get("fy", 1190.0))
        cx = float(self.default_intrinsics.get("cx", W / 2.0))
        cy = float(self.default_intrinsics.get("cy", H / 2.0))
        return {"fx": fx, "fy": fy, "cx": cx, "cy": cy}

    def get_metadata(self) -> Dict:
        if self.metadata_cache is not None:
            return self.metadata_cache
            
        cap = cv2.VideoCapture(self.path)
        try:
            fps, W, H = assert_portrait_and_fps(cap)
            self.metadata_cache = {
                "fps": fps, 
                "width": W, 
                "height": H, 
                "orientation": "portrait"
            }
            return self.metadata_cache
        finally:
            cap.release()