from __future__ import annotations
from typing import Iterable, Dict, Optional, <PERSON>ple
import numpy as np
from abc import ABC, abstractmethod

class VideoValidationError(Exception):
    """Raised when video doesn't meet requirements"""
    pass

class BaseSource(ABC):
    """Base class for all video input sources"""
    
    def __init__(self, source_id: str = None):
        self.source_id = source_id or "unknown"
        self.validated = False
        self.metadata_cache: Optional[Dict] = None
    
    @abstractmethod
    def get_frames(self) -> Iterable[np.ndarray]:
        """Yield video frames as BGR numpy arrays"""
        raise NotImplementedError
    
    @abstractmethod
    def get_intrinsics(self) -> Dict[str, float]:
        """Return camera intrinsics: fx, fy, cx, cy"""
        raise NotImplementedError
    
    @abstractmethod
    def get_metadata(self) -> Dict:
        """Return video metadata: fps, width, height, orientation, etc."""
        raise NotImplementedError
    
    def validate_requirements(self) -> bool:
        """Validate video meets portrait and FPS requirements"""
        if self.validated:
            return True
            
        try:
            metadata = self.get_metadata()
            
            # Check portrait orientation
            if metadata.get("orientation") != "portrait":
                raise VideoValidationError(f"Video must be portrait, got: {metadata.get('orientation')}")
            
            # Check resolution
            width, height = metadata.get("width"), metadata.get("height")
            valid_resolutions = {(1080, 1920), (2160, 3840)}
            if (width, height) not in valid_resolutions:
                raise VideoValidationError(f"Invalid resolution {width}x{height}. "
                                         f"Allowed: 1080x1920 or 2160x3840")
            
            # Check FPS
            fps = metadata.get("fps")
            if fps not in (30, 60):
                raise VideoValidationError(f"Invalid FPS {fps}. Allowed: 30 or 60")
            
            self.validated = True
            return True
            
        except Exception as e:
            raise VideoValidationError(f"Validation failed for {self.source_id}: {str(e)}")
    
    def get_frame_count_estimate(self) -> int:
        """Estimate total frame count (for progress tracking)"""
        metadata = self.get_metadata()
        fps = metadata.get("fps", 30)
        return min(fps * 10, 600)  # Max 10 seconds or 600 frames