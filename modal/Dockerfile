FROM nvidia/cuda:11.8.0-runtime-ubuntu20.04

ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

RUN apt-get update \
 && apt-get install -y --no-install-recommends \
    ffmpeg libsm6 libxext6 python3-pip python3-dev \
 && rm -rf /var/lib/apt/lists/*

# Python deps
COPY modal/requirements.txt /tmp/requirements.txt
RUN pip3 install --no-cache-dir -r /tmp/requirements.txt

# App code
WORKDIR /app
COPY . /app

# Set PYTHONPATH so modal can import adapters/* etc.
ENV PYTHONPATH=/app

# Health check (import onnxruntime + cv2)
RUN python3 -c "import onnxruntime as ort, cv2, numpy; print('Providers:', ort.get_available_providers())"