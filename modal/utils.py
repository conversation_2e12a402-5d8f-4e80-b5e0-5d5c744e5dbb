from __future__ import annotations
import os, json, tempfile
from typing import List, <PERSON>ple
import numpy as np
import cv2
from loguru import logger

PORTRAIT_SIZES = {(1080, 1920), (2160, 3840)}  # (w, h)


def assert_portrait_and_fps(cap: cv2.VideoCapture):
    fps = int(round(cap.get(cv2.CAP_PROP_FPS)))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    if (width, height) not in PORTRAIT_SIZES:
        raise AssertionError(
            f"Unsupported resolution {width}x{height}. Allowed: 1080x1920 or 2160x3840 (portrait)."
        )
    if fps not in (30, 60):
        raise AssertionError(f"Unsupported FPS {fps}. Allowed: 30 or 60.")
    return fps, width, height


def pick_fourcc():
    # Prefer H.264 for browser compatibility; fallback to mp4v
    try:
        return cv2.VideoWriter_fourcc(*"avc1")
    except Exception:
        return cv2.VideoWriter_fourcc(*"mp4v")


def moving_average_boxes(boxes: List[Tuple[int,int,int,int]], k: int = 3):
    if not boxes:
        return []
    k = max(1, k)
    smooth = []
    for i in range(len(boxes)):
        lo = max(0, i - (k - 1))
        hi = i + 1
        win = np.array(boxes[lo:hi], dtype=np.float32)
        m = win.mean(axis=0)
        smooth.append(tuple(map(int, m)))
    return smooth


def calculate_bbox_stability_metrics(boxes: List[Tuple[int,int,int,int]], confidences: List[float] = None):
    """
    Calculate stability metrics for bounding box sequence.
    
    Args:
        boxes: List of (x, y, w, h) bounding boxes
        confidences: Optional list of detection confidences
        
    Returns:
        dict with stability metrics
    """
    if len(boxes) < 2:
        return {"jitter_median": 0, "jitter_max": 0, "jitter_mean": 0, "stability_score": 1.0}
    
    boxes_arr = np.array(boxes, dtype=np.float32)
    
    # Calculate center points for jitter analysis
    centers = boxes_arr[:, :2] + boxes_arr[:, 2:] / 2.0  # (cx, cy)
    
    # Calculate frame-to-frame jitter (Euclidean distance between centers)
    jitters = []
    for i in range(1, len(centers)):
        jitter = np.linalg.norm(centers[i] - centers[i-1])
        jitters.append(jitter)
    
    jitters = np.array(jitters)
    
    # Calculate size stability (width + height changes)
    sizes = boxes_arr[:, 2] + boxes_arr[:, 3]  # w + h
    size_changes = []
    for i in range(1, len(sizes)):
        size_change = abs(sizes[i] - sizes[i-1]) / max(sizes[i-1], 1)
        size_changes.append(size_change)
    
    size_changes = np.array(size_changes)
    
    # Calculate overall stability score (lower jitter + lower size variation = higher score)
    jitter_score = max(0, 1 - np.mean(jitters) / 50.0)  # Normalize by 50px max expected jitter
    size_score = max(0, 1 - np.mean(size_changes))
    stability_score = (jitter_score + size_score) / 2.0
    
    metrics = {
        "jitter_median": float(np.median(jitters)) if len(jitters) > 0 else 0,
        "jitter_max": float(np.max(jitters)) if len(jitters) > 0 else 0,
        "jitter_mean": float(np.mean(jitters)) if len(jitters) > 0 else 0,
        "jitter_std": float(np.std(jitters)) if len(jitters) > 0 else 0,
        "size_variation_mean": float(np.mean(size_changes)) if len(size_changes) > 0 else 0,
        "stability_score": float(stability_score),
        "total_frames": len(boxes),
        "detection_confidence_mean": float(np.mean(confidences)) if confidences else None
    }
    
    return metrics


def detect_person_bbox(frame, return_confidence=False):
    """
    Detect person bounding box with optimized HOG parameters.
    
    Args:
        frame: Input frame (BGR format)
        return_confidence: If True, return (bbox, confidence, detection_type)
        
    Returns:
        (x, y, w, h) or ((x, y, w, h), confidence, detection_type) if return_confidence=True
    """
    # Optimized HOG parameters for portrait videos
    hog = cv2.HOGDescriptor()
    hog.setSVMDetector(cv2.HOGDescriptor_getDefaultPeopleDetector())
    
    # Optimized parameters for better detection in portrait videos
    rects, weights = hog.detectMultiScale(
        frame, 
        winStride=(4, 4),    # Smaller stride for better detection (was 8,8)
        padding=(16, 16),    # More padding for edge cases (was 8,8)  
        scale=1.02,          # Smaller scale step for better detection (was 1.05)
        finalThreshold=1.5,  # Lower threshold for more detections 
        useMeanshiftGrouping=False
    )
    
    if len(rects) == 0:
        # Enhanced fallback center box for portrait videos
        h, w = frame.shape[:2]
        # Portrait-optimized fallback (taller, narrower box)
        box_w = int(w * 0.5)    # Narrower for portrait (was 0.6)
        box_h = int(h * 0.85)   # Slightly smaller height (was 0.9)
        x = (w - box_w) // 2
        y = max(0, int(h * 0.05))  # Start slightly from top
        bbox = (x, y, box_w, box_h)
        
        if return_confidence:
            return bbox, 0.0, "fallback"
        return bbox
    
    # Select best detection (largest area with highest confidence)
    best_idx = 0
    best_score = weights[0] * (rects[0][2] * rects[0][3])  # confidence * area
    
    for i, (rect, weight) in enumerate(zip(rects, weights)):
        score = weight * (rect[2] * rect[3])
        if score > best_score:
            best_score = score
            best_idx = i
    
    bbox = tuple(map(int, rects[best_idx]))
    confidence = float(weights[best_idx])
    
    if return_confidence:
        return bbox, confidence, "hog"
    return bbox


def crop_to_model_input(frame, bbox, target=(256, 192)):
    # Convert portrait (9:16) → center crop near bbox to 4:3
    x, y, w, h = bbox
    H, W = frame.shape[:2]
    # Desired aspect 4:3
    desired_w = int((4/3) * H)  # if we used full height; then clamp
    desired_h = H
    # Clamp to frame bounds and adjust to include bbox center
    cx = x + w // 2
    cy = y + h // 2
    crop_w = min(desired_w, W)
    crop_h = min(desired_h, H)
    # ensure 4:3 ratio exactly
    if crop_w / crop_h > 4/3:
        crop_w = int((4/3) * crop_h)
    else:
        crop_h = int((3/4) * crop_w)
    x1 = max(0, min(W - crop_w, cx - crop_w // 2))
    y1 = max(0, min(H - crop_h, cy - crop_h // 2))
    crop = frame[y1:y1+crop_h, x1:x1+crop_w]
    resized = cv2.resize(crop, target, interpolation=cv2.INTER_LINEAR)
    mapping = {
        "orig_w": W, "orig_h": H, "crop_x": x1, "crop_y": y1, "crop_w": crop_w, "crop_h": crop_h,
        "target_w": target[0], "target_h": target[1]
    }
    return resized, mapping


def remap_to_original(p2d_in_crop, mapping):
    # p2d_in_crop: (N,2) points in 256x192 space → original pixel coords
    sx = mapping["crop_w"] / mapping["target_w"]
    sy = mapping["crop_h"] / mapping["target_h"]
    out = []
    for x_c, y_c in p2d_in_crop:
        x_o = mapping["crop_x"] + x_c * sx
        y_o = mapping["crop_y"] + y_c * sy
        out.append((float(x_o), float(y_o)))
    return out


def write_video(path, frames_iter, fps, size_wh):
    fourcc = pick_fourcc()
    out = cv2.VideoWriter(path, fourcc, fps, size_wh)
    for fr in frames_iter:
        out.write(fr)
    out.release()


def save_json(path, data):
    with open(path, "w") as f:
        json.dump(data, f, indent=2)


def download_s3_to_temp(s3, bucket, key):
    import tempfile
    tmp = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(key)[-1])
    s3.download_fileobj(bucket, key, tmp)
    tmp.close()
    return tmp.name


def validate_preprocessing(original_frame: np.ndarray, resized_frame: np.ndarray, 
                          mapping: dict, target_shape: tuple = (256, 192)) -> dict:
    """
    Validate frame preprocessing maintains aspect ratio and mapping accuracy.
    
    Args:
        original_frame: Original input frame (H, W, 3)
        resized_frame: Preprocessed frame (target_h, target_w, 3)  
        mapping: Crop and resize mapping dictionary
        target_shape: Expected output shape (width, height)
        
    Returns:
        dict with validation results
    """
    validation = {"valid": True, "warnings": [], "errors": []}
    
    # Check output dimensions
    if resized_frame.shape[:2] != (target_shape[1], target_shape[0]):  # (H, W) vs (W, H)
        validation["errors"].append(
            f"Invalid output shape: expected {(target_shape[1], target_shape[0])}, got {resized_frame.shape[:2]}"
        )
        validation["valid"] = False
    
    # Check aspect ratio preservation (4:3 ratio)
    expected_ratio = 4.0 / 3.0
    actual_ratio = mapping["crop_w"] / mapping["crop_h"]
    ratio_error = abs(actual_ratio - expected_ratio) / expected_ratio
    
    if ratio_error > 0.01:  # 1% tolerance
        validation["warnings"].append(
            f"Aspect ratio deviation: expected {expected_ratio:.3f}, got {actual_ratio:.3f} (error: {ratio_error:.1%})"
        )
    
    # Validate mapping coordinates are within original frame bounds
    orig_h, orig_w = original_frame.shape[:2]
    if (mapping["crop_x"] + mapping["crop_w"] > orig_w or 
        mapping["crop_y"] + mapping["crop_h"] > orig_h):
        validation["errors"].append("Crop region exceeds original frame bounds")
        validation["valid"] = False
        
    # Check that crop region is reasonable size (not too small)
    crop_area_ratio = (mapping["crop_w"] * mapping["crop_h"]) / (orig_w * orig_h)
    if crop_area_ratio < 0.1:  # Less than 10% of original
        validation["warnings"].append(f"Very small crop region: {crop_area_ratio:.1%} of original")
        
    # Validate mapping completeness
    required_keys = ["orig_w", "orig_h", "crop_x", "crop_y", "crop_w", "crop_h", "target_w", "target_h"]
    missing_keys = [key for key in required_keys if key not in mapping]
    if missing_keys:
        validation["errors"].append(f"Missing mapping keys: {missing_keys}")
        validation["valid"] = False
    
    # Calculate scaling factors for remapping validation
    scale_x = mapping["crop_w"] / mapping["target_w"] if "target_w" in mapping else 1.0
    scale_y = mapping["crop_h"] / mapping["target_h"] if "target_h" in mapping else 1.0
    
    validation["metrics"] = {
        "aspect_ratio": actual_ratio,
        "aspect_ratio_error": ratio_error,
        "crop_area_ratio": crop_area_ratio,
        "scale_x": scale_x,
        "scale_y": scale_y
    }
    
    return validation


def validate_tensor_preprocessing(tensor: np.ndarray, expected_shape: tuple = (1, 3, 256, 192)) -> dict:
    """
    Validate preprocessed tensor format and values.
    
    Args:
        tensor: Input tensor for model inference
        expected_shape: Expected tensor shape (N, C, H, W)
        
    Returns:
        dict with validation results
    """
    validation = {"valid": True, "warnings": [], "errors": []}
    
    # Check tensor shape
    if tensor.shape != expected_shape:
        validation["errors"].append(f"Invalid tensor shape: expected {expected_shape}, got {tensor.shape}")
        validation["valid"] = False
        
    # Check tensor data type
    if tensor.dtype != np.float32:
        validation["errors"].append(f"Invalid tensor dtype: expected float32, got {tensor.dtype}")
        validation["valid"] = False
        
    # Check value range [0, 1] for normalized RGB
    if tensor.min() < -0.1 or tensor.max() > 1.1:  # Small tolerance for floating point errors
        validation["warnings"].append(f"Values outside [0,1] range: [{tensor.min():.3f}, {tensor.max():.3f}]")
        
    # Check for NaN or inf values
    if np.any(np.isnan(tensor)):
        validation["errors"].append("NaN values detected in tensor")
        validation["valid"] = False
        
    if np.any(np.isinf(tensor)):
        validation["errors"].append("Infinite values detected in tensor")
        validation["valid"] = False
        
    # Channel statistics (RGB channels should have reasonable distributions)
    if len(tensor.shape) == 4 and tensor.shape[1] == 3:  # (N, C, H, W)
        channel_means = [tensor[0, c].mean() for c in range(3)]
        channel_stds = [tensor[0, c].std() for c in range(3)]
        
        # Check if all channels are too similar (might indicate grayscale or processing error)
        if all(abs(m - channel_means[0]) < 0.05 for m in channel_means):
            validation["warnings"].append("All RGB channels very similar - possible grayscale input")
            
        validation["metrics"] = {
            "channel_means": channel_means,
            "channel_stds": channel_stds,
            "value_range": [float(tensor.min()), float(tensor.max())],
            "total_pixels": int(tensor.size)
        }
    
    return validation


def ensure_dir(p):
    os.makedirs(os.path.dirname(p), exist_ok=True)


def validate_pose3d_coordinates(pose3d_mm: np.ndarray, frame_idx: int = 0) -> dict:
    """
    Enhanced validation for 3D pose coordinates.
    
    Args:
        pose3d_mm: Shape (34, 3) pose in millimeters
        frame_idx: Frame index for logging
        
    Returns:
        dict with validation results
    """
    validation = {"valid": True, "warnings": [], "errors": []}
    
    # Basic NaN/inf checks
    if np.any(np.isnan(pose3d_mm)):
        validation["errors"].append("NaN values detected")
        validation["valid"] = False
        
    if np.any(np.isinf(pose3d_mm)):
        validation["errors"].append("Infinite values detected")
        validation["valid"] = False
    
    # Reasonable range checks (human body constraints)
    max_coord = np.abs(pose3d_mm).max()
    if max_coord > 3000:  # 3 meters
        validation["warnings"].append(f"Large coordinate detected: {max_coord:.1f}mm")
        
    # Joint-specific validation (basic anatomical constraints)
    joint_ranges = {
        # Head/neck joints (0-5) should be above torso
        "head_neck": (0, 5, {"z_min": -500, "z_max": 1000}),  # Head should be above shoulders
        # Torso joints (6-17) should be central
        "torso": (6, 17, {"x_max": 800, "y_max": 800, "z_min": -800, "z_max": 800}),
        # Arms (18-25, 26-33) reasonable extension
        "arms": (18, 33, {"x_max": 1500, "y_max": 1500, "z_max": 1500})
    }
    
    for region, (start, end, limits) in joint_ranges.items():
        region_coords = pose3d_mm[start:end+1]
        
        for axis_idx, axis in enumerate(['x', 'y', 'z']):
            coords = region_coords[:, axis_idx]
            
            if f"{axis}_min" in limits and coords.min() < limits[f"{axis}_min"]:
                validation["warnings"].append(
                    f"{region} {axis}-min violation: {coords.min():.1f}mm < {limits[f'{axis}_min']}"
                )
                
            if f"{axis}_max" in limits and coords.max() > limits[f"{axis}_max"]:
                validation["warnings"].append(
                    f"{region} {axis}-max violation: {coords.max():.1f}mm > {limits[f'{axis}_max']}"
                )
    
    # Temporal consistency check (joint velocity)
    validation["max_coordinate"] = float(max_coord)
    validation["coordinate_std"] = float(np.std(pose3d_mm))
    validation["joint_spread"] = float(np.abs(pose3d_mm).mean())
    
    return validation


def calculate_smoothing_quality(raw_coords: List[np.ndarray], smoothed_coords: List[np.ndarray]) -> dict:
    """
    Calculate quality metrics for coordinate smoothing.
    
    Args:
        raw_coords: List of raw (34, 3) coordinates per frame
        smoothed_coords: List of smoothed (34, 3) coordinates per frame
        
    Returns:
        dict with smoothing quality metrics
    """
    if len(raw_coords) < 2 or len(smoothed_coords) < 2:
        return {"valid": False, "error": "Insufficient frames for analysis"}
    
    raw_coords = np.array(raw_coords)  # (frames, 34, 3)
    smoothed_coords = np.array(smoothed_coords)  # (frames, 34, 3)
    
    # Calculate frame-to-frame jitter reduction
    raw_jitter = np.mean([
        np.linalg.norm(raw_coords[i] - raw_coords[i-1])
        for i in range(1, len(raw_coords))
    ])
    
    smooth_jitter = np.mean([
        np.linalg.norm(smoothed_coords[i] - smoothed_coords[i-1])
        for i in range(1, len(smoothed_coords))
    ])
    
    jitter_reduction = (raw_jitter - smooth_jitter) / raw_jitter if raw_jitter > 0 else 0
    
    # Calculate signal preservation (how much of the original signal is retained)
    signal_diff = np.mean(np.abs(raw_coords - smoothed_coords))
    signal_magnitude = np.mean(np.abs(raw_coords))
    signal_preservation = 1 - (signal_diff / signal_magnitude) if signal_magnitude > 0 else 0
    
    return {
        "valid": True,
        "jitter_reduction": float(jitter_reduction),
        "raw_jitter_mm": float(raw_jitter),
        "smooth_jitter_mm": float(smooth_jitter),
        "signal_preservation": float(signal_preservation),
        "signal_diff_mm": float(signal_diff)
    }