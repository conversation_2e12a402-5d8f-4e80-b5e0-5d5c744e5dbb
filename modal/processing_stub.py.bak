from __future__ import annotations
import os, json, time, hashlib
from typing import Dict, Any
import requests

import modal
import boto3
import cv2
import numpy as np
from loguru import logger

from one_euro import OneEuroFilter
from modal import Image
from utils import (
    detect_person_bbox, moving_average_boxes,
    crop_to_model_input, remap_to_original, write_video, save_json,
    download_s3_to_temp, ensure_dir, calculate_bbox_stability_metrics,
    validate_pose3d_coordinates, calculate_smoothing_quality,
    validate_preprocessing, validate_tensor_preprocessing
)
from adapters.upload_source import UploadSource

try:
    import onnxruntime as ort
except Exception:
    ort = None

APP_NAME = "process_running_video"
app = modal.App(APP_NAME)

# Modal Volume for model storage
model_volume = modal.Volume.from_name("bodypose3d-models")

# Use Modal's optimized GPU image instead of custom Dockerfile
image = (
    modal.Image.debian_slim(python_version="3.9")
    .apt_install("ffmpeg", "libsm6", "libxext6")
    .pip_install(
        "onnxruntime-gpu==1.16.3",
        "opencv-python-headless>=4.8.0", 
        "numpy>=1.24.0,<1.26.0",
        "boto3>=1.20.0",
        "pydantic>=2.0.0",
        "pillow>=10.0.0",
        "loguru>=0.7.0",
        "requests>=2.25.0"
    )
)


def send_webhook_notification(job_uuid: str, status: str, view: str = None, artifacts: Dict = None, error: str = None):
    """Send webhook notification to Supabase"""
    webhook_url = os.getenv("SUPABASE_WEBHOOK_URL")
    if not webhook_url:
        return
        
    try:
        webhook_payload = {
            "status": status,
            "uuid": job_uuid,
            "view": view,
            "artifacts": artifacts,
            "error": error
        }
        
        response = requests.post(
            webhook_url,
            json=webhook_payload,
            timeout=10,
            headers={"Content-Type": "application/json"}
        )
        
        if response.ok:
            logger.info(f"Webhook notification sent for job {job_uuid}: {status}")
        else:
            logger.warning(f"Webhook failed: {response.status_code}")
            
    except Exception as e:
        logger.error(f"Webhook error: {e}")


@app.function(gpu="any", image=image, timeout=600, volumes={"/models": model_volume})
def run_inference(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    payload = {
        "s3_bucket": "maxwattz-videos",
        "s3_key": "uploads/UUID/original.mp4",
        "view": "side",
        "job_uuid": "UUID",
        "intrinsics": {"fx":1190.0, "fy":1190.0, "cx":960.0, "cy":540.0},
        "height_cm": 178,
    }
    """
    job_uuid = payload["job_uuid"]
    view = payload.get("view", "side")
    
    try:
        t0 = time.time()

        # S3
        s3 = boto3.client("s3")
        bucket = payload["s3_bucket"]
        key = payload["s3_key"]
        local_video = download_s3_to_temp(s3, bucket, key)

        # Initialize video source with validation
        source = UploadSource(local_video, payload.get("intrinsics", {}))
        source.validate_requirements()  # Validate portrait + FPS requirements
        metadata = source.get_metadata()
        fps, W, H = metadata["fps"], metadata["width"], metadata["height"]

        # Load model from Modal Volume
        model_local = "/models/bodypose3dnet_accuracy.onnx"
        if not os.path.exists(model_local):
            raise RuntimeError(f"Model not found in volume: {model_local}")
        
        # Compute model hash for manifest
        with open(model_local, "rb") as mf:
            model_hash = hashlib.sha256(mf.read()).hexdigest()[:12]

        if ort is None:
            raise RuntimeError("onnxruntime-gpu is not available in this image")

        sess = ort.InferenceSession(model_local, providers=["CUDAExecutionProvider"])  # noqa: F841

    # Skeleton topology (locked file, overridable via env)
    skeleton_path = os.getenv("SKELETON_JSON_PATH", "modal/skeleton_34.json")
    with open(skeleton_path, "r") as f:
        skeleton = json.load(f)
    edges = skeleton.get("edges", [])

    # Processing buffers
    bboxes_raw = []
    detection_confidences = []
    detection_types = []
    frames_orig = []
    preproc = []  # (resized frame, mapping)

    # Read frames (max 10s) with enhanced detection tracking using adapter
    for idx, frame in enumerate(source.get_frames()):
        frames_orig.append(frame)
        
        # Enhanced detection with confidence tracking
        bbox, confidence, detection_type = detect_person_bbox(frame, return_confidence=True)
        bboxes_raw.append(bbox)
        detection_confidences.append(confidence)
        detection_types.append(detection_type)
    
    # Calculate stability metrics before smoothing
    raw_stability = calculate_bbox_stability_metrics(bboxes_raw, detection_confidences)
    
    # Apply smoothing 
    bboxes = moving_average_boxes(bboxes_raw, k=3)
    
    # Calculate stability metrics after smoothing
    smooth_stability = calculate_bbox_stability_metrics(bboxes)
    
    # Log detection quality
    fallback_ratio = detection_types.count('fallback') / len(detection_types) if detection_types else 0
    logger.info(f"Detection quality: {len(bboxes)} frames, "
                f"fallback ratio: {fallback_ratio:.2%}, "
                f"raw jitter: {raw_stability['jitter_median']:.1f}px, "
                f"smooth jitter: {smooth_stability['jitter_median']:.1f}px, "
                f"stability score: {smooth_stability['stability_score']:.3f}")

    # Preprocess to 256x192 with 4:3 crop centered on bbox with validation
    preprocessing_validations = []
    for i, (fr, bb) in enumerate(zip(frames_orig, bboxes)):
        resized, mapping = crop_to_model_input(fr, bb, target=(256, 192))
        
        # Validate preprocessing for first frame and every 30th frame
        if i == 0 or (i + 1) % 30 == 0:
            prep_validation = validate_preprocessing(fr, resized, mapping)
            preprocessing_validations.append(prep_validation)
            
            if not prep_validation["valid"]:
                raise RuntimeError(f"Frame {i}: Preprocessing validation failed: {prep_validation['errors']}")
            
            if prep_validation["warnings"] and i == 0:  # Log first frame warnings
                logger.warning(f"Frame {i}: Preprocessing warnings: {prep_validation['warnings'][:2]}")
        
        preproc.append((resized, mapping))
    
    # Log preprocessing validation summary
    if preprocessing_validations:
        avg_aspect_ratio = np.mean([v["metrics"]["aspect_ratio"] for v in preprocessing_validations if "metrics" in v])
        avg_crop_area = np.mean([v["metrics"]["crop_area_ratio"] for v in preprocessing_validations if "metrics" in v])
        logger.info(f"Preprocessing validation: avg aspect ratio={avg_aspect_ratio:.3f}, avg crop area={avg_crop_area:.1%}")

    # Inference loop
    # NOTE: Real model I/O will differ; assuming single input called "input"
    smoothed_m = []
    filters = [[OneEuroFilter(freq=fps) for _ in range(3)] for _ in range(34)]

    # outputs to serialize
    keypoints_3d_m = []
    raw_coordinates_mm = []  # Track raw coordinates for smoothing quality analysis

    # Get intrinsics from source adapter (with defaults for debug)
    intrinsics = source.get_intrinsics()
    fx, fy, cx, cy = intrinsics["fx"], intrinsics["fy"], intrinsics["cx"], intrinsics["cy"]
    
    # Camera matrix K and inverse
    K = np.array([[fx, 0, cx], [0, fy, cy], [0, 0, 1]], dtype=np.float32)
    K_inv = np.linalg.inv(K)
    
    # Transform matrices (identity for now)
    t_form_inv = np.eye(3, dtype=np.float32)
    
    # Default limb lengths (36 limb segments)
    mean_limb_lengths = np.ones((36,), dtype=np.float32) * 0.3  # 30cm average
    scale_normalized_mean_limb_lengths = mean_limb_lengths / np.mean(mean_limb_lengths)

    # Timing instrumentation
    inference_times = []
    preprocessing_times = []
    
    for i, (fr256, mapping) in enumerate(preproc):
        preproc_start = time.time()
        
        # prepare input tensor NCHW (1,3,256,192)
        fr_rgb = cv2.cvtColor(fr256, cv2.COLOR_BGR2RGB).astype(np.float32) / 255.0
        chw = np.transpose(fr_rgb, (2, 0, 1))
        inp = np.expand_dims(chw, 0).astype(np.float32)
        
        # Enhanced tensor validation for first frame and every 60 frames
        if i == 0 or (i + 1) % 60 == 0:
            tensor_validation = validate_tensor_preprocessing(inp)
            if not tensor_validation["valid"]:
                raise RuntimeError(f"Frame {i}: Tensor validation failed: {tensor_validation['errors']}")
            
            if tensor_validation["warnings"] and i == 0:
                logger.warning(f"Frame {i}: Tensor warnings: {tensor_validation['warnings']}")
                
            # Log tensor metrics for first frame
            if i == 0 and "metrics" in tensor_validation:
                metrics = tensor_validation["metrics"]
                logger.info(f"Tensor validation: shape={inp.shape}, range={metrics['value_range']}, "
                           f"RGB means={[f'{m:.3f}' for m in metrics['channel_means']]}")
        else:
            # Quick validation for other frames
            assert inp.shape == (1, 3, 256, 192), f"Invalid input shape: {inp.shape}"
            assert 0.0 <= inp.min() and inp.max() <= 1.0, f"Invalid input range: [{inp.min()}, {inp.max()}]"
        
        # Prepare all model inputs
        input_dict = {
            "input0": inp,
            "k_inv": np.expand_dims(K_inv, 0),
            "t_form_inv": np.expand_dims(t_form_inv, 0),
            "scale_normalized_mean_limb_lengths": np.expand_dims(scale_normalized_mean_limb_lengths, 0),
            "mean_limb_lengths": np.expand_dims(mean_limb_lengths, 0)
        }
        
        # Validate auxiliary inputs
        assert input_dict["k_inv"].shape == (1, 3, 3), "Invalid k_inv shape"
        assert input_dict["t_form_inv"].shape == (1, 3, 3), "Invalid t_form_inv shape"
        assert input_dict["scale_normalized_mean_limb_lengths"].shape == (1, 36), "Invalid limb lengths shape"
        assert input_dict["mean_limb_lengths"].shape == (1, 36), "Invalid mean limb lengths shape"
        
        preproc_time = time.time() - preproc_start
        preprocessing_times.append(preproc_time)

        # Run inference with timing and error handling
        inference_start = time.time()
        try:
            outs = sess.run(None, input_dict)
        except Exception as e:
            raise RuntimeError(f"ONNX inference failed on frame {i}: {str(e)}")
        
        inference_time = time.time() - inference_start
        inference_times.append(inference_time)
        
        # Log performance for first frame and every 30 frames
        if i == 0 or (i + 1) % 30 == 0:
            logger.info(f"Frame {i}: preproc={preproc_time*1000:.1f}ms, inference={inference_time*1000:.1f}ms")
        # Validate model outputs
        if len(outs) != 4:
            raise RuntimeError(f"Expected 4 model outputs, got {len(outs)}")
        
        # BodyPose3DNet outputs: [pose2d, pose2d_org_img, pose25d, pose3d]
        # Expected shapes: [(1,34,3), (1,34,3), (1,34,4), (1,34,3)]
        expected_shapes = [(1, 34, 3), (1, 34, 3), (1, 34, 4), (1, 34, 3)]
        output_names = ["pose2d", "pose2d_org_img", "pose25d", "pose3d"]
        
        for idx, (out, expected_shape, name) in enumerate(zip(outs, expected_shapes, output_names)):
            if out.shape != expected_shape:
                raise RuntimeError(f"Invalid {name} shape: expected {expected_shape}, got {out.shape}")
        
        pose2d = outs[0][0]
        pose2d_org_img = outs[1][0]
        pose25d = outs[2][0] 
        pose3d = outs[3][0]
        
        # Enhanced coordinate validation
        validation = validate_pose3d_coordinates(pose3d, frame_idx=i)
        if not validation["valid"]:
            raise RuntimeError(f"Frame {i}: Coordinate validation failed: {validation['errors']}")
        
        # Log warnings for anatomical constraint violations
        if validation["warnings"] and i % 30 == 0:  # Log every 30 frames to avoid spam
            logger.warning(f"Frame {i}: {len(validation['warnings'])} anatomical warnings: {validation['warnings'][:2]}")
        
        # Store raw coordinates for smoothing quality analysis
        raw_coordinates_mm.append(pose3d.copy())
        
        # Use pose3d (in mm) and pose2d_org_img if available
        pose3d_mm = pose3d
        pose2d_crop = pose2d_org_img[:, :2] if pose2d_org_img is not None else None

        # Smooth and convert once mm→m
        joints_m = []
        for j in range(34):
            x_mm, y_mm, z_mm = pose3d_mm[j].tolist()
            sx = filters[j][0](x_mm)
            sy = filters[j][1](y_mm)
            sz = filters[j][2](z_mm)
            joints_m.append([sx / 1000.0, sy / 1000.0, sz / 1000.0])
        keypoints_3d_m.append(joints_m)

        # attach remapped 2D if present
        if pose2d_crop is not None:
            keypoints_2d_org = remap_to_original(pose2d_crop, mapping)
            preproc[i] = (fr256, mapping, keypoints_2d_org)
        else:
            preproc[i] = (fr256, mapping, None)

    # Calculate smoothing quality metrics
    if len(raw_coordinates_mm) > 1 and len(keypoints_3d_m) > 1:
        # Convert keypoints_3d_m back to mm for comparison
        smoothed_coords_mm = [
            np.array([[joint[0]*1000, joint[1]*1000, joint[2]*1000] for joint in frame_joints])
            for frame_joints in keypoints_3d_m
        ]
        
        smoothing_quality = calculate_smoothing_quality(raw_coordinates_mm, smoothed_coords_mm)
        if smoothing_quality["valid"]:
            logger.info(f"Smoothing quality: jitter reduction={smoothing_quality['jitter_reduction']:.1%}, "
                       f"signal preservation={smoothing_quality['signal_preservation']:.1%}, "
                       f"raw jitter={smoothing_quality['raw_jitter_mm']:.1f}mm, "
                       f"smooth jitter={smoothing_quality['smooth_jitter_mm']:.1f}mm")
    else:
        smoothing_quality = {"valid": False, "error": "Insufficient data"}

    # Compose overlay video at original resolution
    out_dir = f"/tmp/bodypose3dnet/{view}/{job_uuid}/"
    ensure_dir(out_dir + "x")
    out_mp4 = os.path.join(out_dir, "annotated.mp4")
    out_json = os.path.join(out_dir, "keypoints_3d.json")
    manifest_path = os.path.join(out_dir, "manifest.json")

    def frame_stream():
        for (fr256, mapping, pts2d), fr_orig in zip(preproc, frames_orig):
            canvas = fr_orig.copy()
            if pts2d is None:
                # draw smoothed bbox only (placeholder when 2D keypoints not exposed by model)
                x, y, w, h = mapping["crop_x"], mapping["crop_y"], mapping["crop_w"], mapping["crop_h"]
                cv2.rectangle(canvas, (x, y), (x + w, y + h), (0, 255, 255), 2)
                cv2.putText(canvas, "POSE2D MISSING", (20, 60), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 255), 2)
            else:
                # draw joints
                for p in pts2d:
                    cv2.circle(canvas, (int(p[0]), int(p[1])), 4, (255, 255, 0), -1)
                # draw edges if any
                for a, b in edges:
                    if 0 <= a < len(pts2d) and 0 <= b < len(pts2d):
                        pa, pb = pts2d[a], pts2d[b]
                        cv2.line(canvas, (int(pa[0]), int(pa[1])), (int(pb[0]), int(pb[1])), (30, 144, 255), 2)
            yield canvas

    write_video(out_mp4, frame_stream(), fps=fps, size_wh=(W, H))

    # Serialize JSON (meters)
    frames_out = []
    for i, joints in enumerate(keypoints_3d_m):
        ts = i / float(fps)
        frames_out.append({
            "timestamp": round(ts, 6),
            "joints": [{"index": j, "x": float(x), "y": float(y), "z": float(z), "confidence": 1.0}
                        for j, (x, y, z) in enumerate(joints)]
        })
    save_json(out_json, frames_out)

    # Upload artifacts back to S3
    prefix = f"running-metrics-{view}/{job_uuid}/"
    s3.upload_file(out_mp4, bucket, prefix + "annotated.mp4")
    s3.upload_file(out_json, bucket, prefix + "keypoints_3d.json")

    # Manifest
    manifest = {
        "job_uuid": job_uuid,
        "view": view,
        "fps": fps,
        "width": W,
        "height": H,
        "orientation": "portrait",
        "model_hash": model_hash,
        "skeleton_file": os.path.basename(skeleton_path),
        "skeleton_hash": hashlib.sha256(open(skeleton_path, 'rb').read()).hexdigest()[:12],
        "inputs": {"bucket": bucket, "key": key},
        "outputs": {
            "annotated_mp4": f"s3://{bucket}/{prefix}annotated.mp4",
            "keypoints_3d_json": f"s3://{bucket}/{prefix}keypoints_3d.json"
        },
        "durations": {
            "total_sec": round(time.time() - t0, 3),
            "inference": {
                "avg_ms": round(np.mean(inference_times) * 1000, 2) if inference_times else 0,
                "max_ms": round(np.max(inference_times) * 1000, 2) if inference_times else 0,
                "min_ms": round(np.min(inference_times) * 1000, 2) if inference_times else 0,
                "total_ms": round(np.sum(inference_times) * 1000, 2) if inference_times else 0
            },
            "preprocessing": {
                "avg_ms": round(np.mean(preprocessing_times) * 1000, 2) if preprocessing_times else 0,
                "max_ms": round(np.max(preprocessing_times) * 1000, 2) if preprocessing_times else 0,
                "total_ms": round(np.sum(preprocessing_times) * 1000, 2) if preprocessing_times else 0
            },
            "frames_processed": len(inference_times),
            "fps_achieved": round(len(inference_times) / np.sum(inference_times), 2) if inference_times else 0
        },
        "detection": {
            "raw_stability": raw_stability,
            "smooth_stability": smooth_stability,
            "fallback_ratio": detection_types.count('fallback') / len(detection_types) if detection_types else 0,
            "hog_detections": detection_types.count('hog') if detection_types else 0,
            "total_detections": len(detection_types)
        },
        "coordinate_smoothing": smoothing_quality,
        "preprocessing": {
            "validations_performed": len(preprocessing_validations),
            "avg_aspect_ratio": float(np.mean([v["metrics"]["aspect_ratio"] for v in preprocessing_validations if "metrics" in v])) if preprocessing_validations else None,
            "avg_crop_area_ratio": float(np.mean([v["metrics"]["crop_area_ratio"] for v in preprocessing_validations if "metrics" in v])) if preprocessing_validations else None,
            "target_shape": [256, 192],
            "expected_aspect_ratio": 4.0/3.0
        }
    }
    save_json(manifest_path, manifest)
    s3.upload_file(manifest_path, bucket, prefix + "manifest.json")

        # Notify completion via webhook
        send_webhook_notification(job_uuid, "ok", view, manifest["outputs"])

        return {"status": "ok", "uuid": job_uuid, "view": view, "artifacts": manifest["outputs"]}
        
    except Exception as e:
        error_message = f"Processing failed: {str(e)}"
        logger.error(f"Job {job_uuid} failed: {error_message}")
        
        # Send failure notification
        send_webhook_notification(job_uuid, "error", view, error=error_message)
        
        # Re-raise the exception so Modal marks it as failed
        raise