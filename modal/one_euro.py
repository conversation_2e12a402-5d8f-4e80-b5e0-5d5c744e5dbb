# Minimal One-Euro filter implementation (per axis per joint)
from dataclasses import dataclass
import math

@dataclass
class OneEuroFilter:
    freq: float = 60.0
    min_cutoff: float = 1.0
    beta: float = 0.0
    dcutoff: float = 1.0

    def __post_init__(self):
        self.x_prev = None
        self.dx_prev = 0.0

    def _alpha(self, cutoff):
        te = 1.0 / self.freq
        tau = 1.0 / (2 * math.pi * cutoff)
        return 1.0 / (1.0 + tau / te)

    def __call__(self, x):
        # derivative of the signal
        if self.x_prev is None:
            self.x_prev = x
        dx = (x - self.x_prev) * self.freq
        a_d = self._alpha(self.dcutoff)
        dx_hat = a_d * dx + (1 - a_d) * self.dx_prev
        # adaptive cutoff
        cutoff = self.min_cutoff + self.beta * abs(dx_hat)
        a = self._alpha(cutoff)
        x_hat = a * x + (1 - a) * (self.x_prev)
        self.x_prev = x_hat
        self.dx_prev = dx_hat
        return x_hat