// /api/init_upload_session.ts
import { NextRequest, NextResponse } from "next/server";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { createPresignedPost } from "@aws-sdk/s3-presigned-post";

export async function POST(req: NextRequest) {
  const bucket = process.env.S3_BUCKET!;
  const uuid = crypto.randomUUID();
  const key = `uploads/${uuid}/original.mp4`;

  const s3 = new S3Client({ region: process.env.AWS_REGION });
  const { url, fields } = await createPresignedPost(s3, {
    Bucket: bucket,
    Key: key,
    Conditions: [["content-length-range", 1, 600000000]],
    Expires: 600,
  });

  const intrinsics = { fx: 1190, fy: 1190, cx: 540, cy: 960 }; // portrait defaults
  return NextResponse.json({ video_id: uuid, s3_bucket: bucket, s3_key: key, url, fields, intrinsics });
}