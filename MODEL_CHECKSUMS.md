# Model Checksums

This document contains cryptographic checksums for all models used in the BodyPose3DNet Modal Pipeline.

## NVIDIA BodyPose3DNet

### bodypose3dnet_accuracy.onnx
- **Source**: NVIDIA NGC `nvidia/tao/bodypose3dnet:deployable_accuracy_onnx_v1.0`
- **Downloaded**: 2025-08-10
- **Size**: 70,575,749 bytes (70.6 MB)
- **Format**: ONNX
- **SHA256**: `0452b785a70fcd6bc5bd4069249bdfd85eb139c9e9216bcf81f89df33945d028`
- **Modal Volume**: `bodypose3d-models/bodypose3dnet_accuracy.onnx`

### Model Specifications
- **Input Shape**: [batch, 3, 256, 192] + auxiliary tensors
- **Output Shape**: [batch, 34, 3] for pose3d
- **Joints**: 34 keypoints in 3D space
- **Coordinate System**: Millimeters (converted to meters in pipeline)
- **Architecture**: HRNet-based

### Additional Inputs
- `k_inv`: Camera intrinsics inverse matrix [batch, 3, 3]
- `t_form_inv`: Transform matrix inverse [batch, 3, 3] 
- `scale_normalized_mean_limb_lengths`: Normalized limb lengths [batch, 36]
- `mean_limb_lengths`: Mean limb lengths [batch, 36]

### Outputs
- `pose2d`: 2D keypoints in crop coordinates [batch, 34, 3]
- `pose2d_org_img`: 2D keypoints in original image coordinates [batch, 34, 3]
- `pose25d`: 2.5D keypoints with depth [batch, 34, 4]  
- `pose3d`: 3D keypoints in millimeters [batch, 34, 3]

## Verification

To verify model integrity:

```bash
shasum -a 256 bodypose3dnet_accuracy.onnx
# Expected: 0452b785a70fcd6bc5bd4069249bdfd85eb139c9e9216bcf81f89df33945d028
```

## License

All NVIDIA TAO models are licensed under the Creative Commons Attribution 4.0 International License.
See: http://creativecommons.org/licenses/by/4.0/

## Last Updated

2025-08-10 - Phase 2 Task 2.1 implementation