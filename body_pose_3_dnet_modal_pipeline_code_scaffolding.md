Below are the files for the initial scaffolding. Copy this folder into your repo as `bodypose3dnet-pipeline/` and deploy per README.

---

## modal/Dockerfile

```dockerfile
FROM nvidia/cuda:11.8.0-runtime-ubuntu20.04

ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

RUN apt-get update \
 && apt-get install -y --no-install-recommends \
    ffmpeg libsm6 libxext6 python3-pip python3-dev \
 && rm -rf /var/lib/apt/lists/*

# Python deps
COPY modal/requirements.txt /tmp/requirements.txt
RUN pip3 install --no-cache-dir -r /tmp/requirements.txt

# App code
WORKDIR /app
COPY . /app

# Set PYTHONPATH so modal can import adapters/* etc.
ENV PYTHONPATH=/app

# Health check (import onnxruntime + cv2)
RUN python3 - <<'PY'
import onnxruntime as ort, cv2, numpy
print('Providers:', ort.get_available_providers())
PY
```

---

## modal/requirements.txt

```text
modal-client==0.62.220
onnxruntime-gpu==1.17.1
opencv-python-headless==*********
numpy==1.26.4
boto3==1.34.131
pydantic==2.8.2
pillow==10.4.0
loguru==0.7.2
```

---

## modal/one\_euro.py

```python
# Minimal One-Euro filter implementation (per axis per joint)
from dataclasses import dataclass
import math

@dataclass
class OneEuroFilter:
    freq: float = 60.0
    min_cutoff: float = 1.0
    beta: float = 0.0
    dcutoff: float = 1.0

    def __post_init__(self):
        self.x_prev = None
        self.dx_prev = 0.0

    def _alpha(self, cutoff):
        te = 1.0 / self.freq
        tau = 1.0 / (2 * math.pi * cutoff)
        return 1.0 / (1.0 + tau / te)

    def __call__(self, x):
        # derivative of the signal
        if self.x_prev is None:
            self.x_prev = x
        dx = (x - self.x_prev) * self.freq
        a_d = self._alpha(self.dcutoff)
        dx_hat = a_d * dx + (1 - a_d) * self.dx_prev
        # adaptive cutoff
        cutoff = self.min_cutoff + self.beta * abs(dx_hat)
        a = self._alpha(cutoff)
        x_hat = a * x + (1 - a) * (self.x_prev)
        self.x_prev = x_hat
        self.dx_prev = dx_hat
        return x_hat
```

---

## modal/utils.py

```python
from __future__ import annotations
import os, json, tempfile
from typing import List, Tuple
import numpy as np
import cv2
from loguru import logger

PORTRAIT_SIZES = {(1080, 1920), (2160, 3840)}  # (w, h)


def assert_portrait_and_fps(cap: cv2.VideoCapture):
    fps = int(round(cap.get(cv2.CAP_PROP_FPS)))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    if (width, height) not in PORTRAIT_SIZES:
        raise AssertionError(
            f"Unsupported resolution {width}x{height}. Allowed: 1080x1920 or 2160x3840 (portrait)."
        )
    if fps not in (30, 60):
        raise AssertionError(f"Unsupported FPS {fps}. Allowed: 30 or 60.")
    return fps, width, height


def pick_fourcc():
    # Prefer H.264 for browser compatibility; fallback to mp4v
    try:
        return cv2.VideoWriter_fourcc(*"avc1")
    except Exception:
        return cv2.VideoWriter_fourcc(*"mp4v")


def moving_average_boxes(boxes: List[Tuple[int,int,int,int]], k: int = 3):
    if not boxes:
        return []
    k = max(1, k)
    smooth = []
    for i in range(len(boxes)):
        lo = max(0, i - (k - 1))
        hi = i + 1
        win = np.array(boxes[lo:hi], dtype=np.float32)
        m = win.mean(axis=0)
        smooth.append(tuple(map(int, m)))
    return smooth


def detect_person_bbox(frame):
    # Simple HOG fallback; replace with PeopleNet later
    hog = cv2.HOGDescriptor()
    hog.setSVMDetector(cv2.HOGDescriptor_getDefaultPeopleDetector())
    rects, _ = hog.detectMultiScale(frame, winStride=(8, 8), padding=(8, 8), scale=1.05)
    if len(rects) == 0:
        # fallback center box (covers most of frame)
        h, w = frame.shape[:2]
        box_w = int(w * 0.6)
        box_h = int(h * 0.9)
        x = (w - box_w) // 2
        y = (h - box_h) // 2
        return (x, y, box_w, box_h)
    # take the largest box
    rects = sorted(rects, key=lambda r: r[2] * r[3], reverse=True)
    return tuple(map(int, rects[0]))


def crop_to_model_input(frame, bbox, target=(256, 192)):
    # Convert portrait (9:16) → center crop near bbox to 4:3
    x, y, w, h = bbox
    H, W = frame.shape[:2]
    # Desired aspect 4:3
    desired_w = int((4/3) * H)  # if we used full height; then clamp
    desired_h = H
    # Clamp to frame bounds and adjust to include bbox center
    cx = x + w // 2
    cy = y + h // 2
    crop_w = min(desired_w, W)
    crop_h = min(desired_h, H)
    # ensure 4:3 ratio exactly
    if crop_w / crop_h > 4/3:
        crop_w = int((4/3) * crop_h)
    else:
        crop_h = int((3/4) * crop_w)
    x1 = max(0, min(W - crop_w, cx - crop_w // 2))
    y1 = max(0, min(H - crop_h, cy - crop_h // 2))
    crop = frame[y1:y1+crop_h, x1:x1+crop_w]
    resized = cv2.resize(crop, target, interpolation=cv2.INTER_LINEAR)
    mapping = {
        "orig_w": W, "orig_h": H, "crop_x": x1, "crop_y": y1, "crop_w": crop_w, "crop_h": crop_h,
        "target_w": target[0], "target_h": target[1]
    }
    return resized, mapping


def remap_to_original(p2d_in_crop, mapping):
    # p2d_in_crop: (N,2) points in 256x192 space → original pixel coords
    sx = mapping["crop_w"] / mapping["target_w"]
    sy = mapping["crop_h"] / mapping["target_h"]
    out = []
    for x_c, y_c in p2d_in_crop:
        x_o = mapping["crop_x"] + x_c * sx
        y_o = mapping["crop_y"] + y_c * sy
        out.append((float(x_o), float(y_o)))
    return out


def write_video(path, frames_iter, fps, size_wh):
    fourcc = pick_fourcc()
    out = cv2.VideoWriter(path, fourcc, fps, size_wh)
    for fr in frames_iter:
        out.write(fr)
    out.release()


def save_json(path, data):
    with open(path, "w") as f:
        json.dump(data, f, indent=2)


def download_s3_to_temp(s3, bucket, key):
    import tempfile
    tmp = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(key)[-1])
    s3.download_fileobj(bucket, key, tmp)
    tmp.close()
    return tmp.name


def ensure_dir(p):
    os.makedirs(os.path.dirname(p), exist_ok=True)
```

---

## modal/processing\_stub.py

```python
from __future__ import annotations
import os, json, time, hashlib
from typing import Dict, Any

import modal
import boto3
import cv2
import numpy as np
from loguru import logger

from modal.one_euro import OneEuroFilter
from modal import Image
from modal.utils import (
    assert_portrait_and_fps, detect_person_bbox, moving_average_boxes,
    crop_to_model_input, remap_to_original, write_video, save_json,
    download_s3_to_temp, ensure_dir
)

try:
    import onnxruntime as ort
except Exception:
    ort = None

STUB_NAME = "process_running_video"
stub = modal.Stub(STUB_NAME)

image = Image.from_dockerfile("./modal/Dockerfile")


@stub.function(gpu="any", image=image, timeout=600)
def run_inference(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    payload = {
        "s3_bucket": "maxwattz-videos",
        "s3_key": "uploads/UUID/original.mp4",
        "view": "side",
        "job_uuid": "UUID",
        "intrinsics": {"fx":1190.0, "fy":1190.0, "cx":960.0, "cy":540.0},
        "height_cm": 178,
    }
    """
    t0 = time.time()
    job_uuid = payload["job_uuid"]
    view = payload.get("view", "side")

    # S3
    s3 = boto3.client("s3")
    bucket = payload["s3_bucket"]
    key = payload["s3_key"]
    local_video = download_s3_to_temp(s3, bucket, key)

    # Open video & validate portrait + fps
    cap = cv2.VideoCapture(local_video)
    fps, W, H = assert_portrait_and_fps(cap)  # 1080x1920 or 2160x3840; 30/60 FPS

    # Load model from S3 if provided
    model_local = None
    model_s3_key = os.getenv("BODYPOSE3DNET_MODEL_S3_KEY")
    if model_s3_key:
        model_local = download_s3_to_temp(s3, bucket, model_s3_key)
    model_hash = None
    if model_local:
        with open(model_local, "rb") as mf:
            model_hash = hashlib.sha256(mf.read()).hexdigest()[:12]

    if ort is None:
        raise RuntimeError("onnxruntime-gpu is not available in this image")
    if not model_local:
        raise RuntimeError("BODYPOSE3DNET_MODEL_S3_KEY must point to the ONNX model in S3")

    sess = ort.InferenceSession(model_local, providers=["CUDAExecutionProvider"])  # noqa: F841

    # Skeleton topology (locked file, overridable via env)
    skeleton_path = os.getenv("SKELETON_JSON_PATH", "modal/skeleton_34.json")
    with open(skeleton_path, "r") as f:
        skeleton = json.load(f)
    edges = skeleton.get("edges", [])

    # Processing buffers
    bboxes = []
    frames_orig = []
    preproc = []  # (resized frame, mapping)

    # Read frames (max 10s)
    max_frames = fps * 10
    idx = 0
    while idx < max_frames:
        ret, frame = cap.read()
        if not ret:
            break
        frames_orig.append(frame)
        bbox = detect_person_bbox(frame)
        bboxes.append(bbox)
        idx += 1
    cap.release()

    bboxes = moving_average_boxes(bboxes, k=3)

    # Preprocess to 256x192 with 4:3 crop centered on bbox
    for fr, bb in zip(frames_orig, bboxes):
        resized, mapping = crop_to_model_input(fr, bb, target=(256, 192))
        preproc.append((resized, mapping))

    # Inference loop
    # NOTE: Real model I/O will differ; assuming single input called "input"
    smoothed_m = []
    filters = [[OneEuroFilter(freq=fps) for _ in range(3)] for _ in range(34)]

    # outputs to serialize
    keypoints_3d_m = []

    for i, (fr256, mapping) in enumerate(preproc):
        # prepare input tensor NCHW (1,3,256,192)
        fr_rgb = cv2.cvtColor(fr256, cv2.COLOR_BGR2RGB).astype(np.float32) / 255.0
        chw = np.transpose(fr_rgb, (2, 0, 1))
        inp = np.expand_dims(chw, 0).astype(np.float32)

        outs = sess.run(None, {sess.get_inputs()[0].name: inp})
        # BodyPose3DNet commonly outputs at least 3D joints in mm.
        # We try to locate a (34,3) tensor in outs.
        pose3d_mm = None
        pose2d_crop = None
        for o in outs:
            if o.ndim == 3 and o.shape[-2:] == (34, 3):
                pose3d_mm = o[0]
            if o.ndim == 3 and o.shape[-2:] == (34, 2):
                pose2d_crop = o[0]
        if pose3d_mm is None:
            raise RuntimeError("Model outputs did not include a (34,3) 3D joints tensor.")

        # Smooth and convert once mm→m
        joints_m = []
        for j in range(34):
            x_mm, y_mm, z_mm = pose3d_mm[j].tolist()
            sx = filters[j][0](x_mm)
            sy = filters[j][1](y_mm)
            sz = filters[j][2](z_mm)
            joints_m.append([sx / 1000.0, sy / 1000.0, sz / 1000.0])
        keypoints_3d_m.append(joints_m)

        # attach remapped 2D if present
        if pose2d_crop is not None:
            keypoints_2d_org = remap_to_original(pose2d_crop, mapping)
            preproc[i] = (fr256, mapping, keypoints_2d_org)
        else:
            preproc[i] = (fr256, mapping, None)

    # Compose overlay video at original resolution
    out_dir = f"/tmp/bodypose3dnet/{view}/{job_uuid}/"
    ensure_dir(out_dir + "x")
    out_mp4 = os.path.join(out_dir, "annotated.mp4")
    out_json = os.path.join(out_dir, "keypoints_3d.json")
    manifest_path = os.path.join(out_dir, "manifest.json")

    def frame_stream():
        for (fr256, mapping, pts2d), fr_orig in zip(preproc, frames_orig):
            canvas = fr_orig.copy()
            if pts2d is None:
                # draw smoothed bbox only (placeholder when 2D keypoints not exposed by model)
                x, y, w, h = mapping["crop_x"], mapping["crop_y"], mapping["crop_w"], mapping["crop_h"]
                cv2.rectangle(canvas, (x, y), (x + w, y + h), (0, 255, 255), 2)
                cv2.putText(canvas, "POSE2D MISSING", (20, 60), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 255), 2)
            else:
                # draw joints
                for p in pts2d:
                    cv2.circle(canvas, (int(p[0]), int(p[1])), 4, (255, 255, 0), -1)
                # draw edges if any
                for a, b in edges:
                    if 0 <= a < len(pts2d) and 0 <= b < len(pts2d):
                        pa, pb = pts2d[a], pts2d[b]
                        cv2.line(canvas, (int(pa[0]), int(pa[1])), (int(pb[0]), int(pb[1])), (30, 144, 255), 2)
            yield canvas

    write_video(out_mp4, frame_stream(), fps=fps, size_wh=(W, H))

    # Serialize JSON (meters)
    frames_out = []
    for i, joints in enumerate(keypoints_3d_m):
        ts = i / float(fps)
        frames_out.append({
            "timestamp": round(ts, 6),
            "joints": [{"index": j, "x": float(x), "y": float(y), "z": float(z), "confidence": 1.0}
                        for j, (x, y, z) in enumerate(joints)]
        })
    save_json(out_json, frames_out)

    # Upload artifacts back to S3
    prefix = f"running-metrics-{view}/{job_uuid}/"
    s3.upload_file(out_mp4, bucket, prefix + "annotated.mp4")
    s3.upload_file(out_json, bucket, prefix + "keypoints_3d.json")

    # Manifest
    manifest = {
        "job_uuid": job_uuid,
        "view": view,
        "fps": fps,
        "width": W,
        "height": H,
        "orientation": "portrait",
        "model_hash": model_hash,
        "skeleton_file": os.path.basename(skeleton_path),
        "skeleton_hash": hashlib.sha256(open(skeleton_path, 'rb').read()).hexdigest()[:12],
        "inputs": {"bucket": bucket, "key": key},
        "outputs": {
            "annotated_mp4": f"s3://{bucket}/{prefix}annotated.mp4",
            "keypoints_3d_json": f"s3://{bucket}/{prefix}keypoints_3d.json"
        },
        "durations": {"total_sec": round(time.time() - t0, 3)}
    }
    save_json(manifest_path, manifest)
    s3.upload_file(manifest_path, bucket, prefix + "manifest.json")

    return {"status": "ok", "uuid": job_uuid, "view": view, "artifacts": manifest["outputs"]}
```

---

## modal/skeleton\_34.json

> **Note:** Placeholder topology (lock v1). Replace indices to match BodyPose3DNet official order when ready. The code logs the file hash into `manifest.json` so overlays are reproducible.

```json
{
  "version": "v1-placeholder",
  "names": [
    "joint_0","joint_1","joint_2","joint_3","joint_4","joint_5","joint_6","joint_7","joint_8","joint_9",
    "joint_10","joint_11","joint_12","joint_13","joint_14","joint_15","joint_16","joint_17","joint_18","joint_19",
    "joint_20","joint_21","joint_22","joint_23","joint_24","joint_25","joint_26","joint_27","joint_28","joint_29",
    "joint_30","joint_31","joint_32","joint_33"
  ],
  "edges": [
    [0,1],[1,2],[2,3],           
    [0,4],[4,5],[5,6],           
    [0,7],[7,8],[8,9],           
    [9,10],[10,11],              
    [0,12],[12,13],[13,14],      
    [14,15],                     
    [7,16],[16,17],[17,18],      
    [8,19],[19,20],[20,21],      
    [12,22],[22,23],[23,24],     
    [13,25],[25,26],[26,27],     
    [14,28],[28,29],             
    [15,30],[30,31],             
    [29,32],[31,33]
  ]
}
```

---

## adapters/base\_source.py

```python
from __future__ import annotations
from typing import Iterable, Dict
import numpy as np

class BaseSource:
    def get_frames(self) -> Iterable[np.ndarray]:
        raise NotImplementedError

    def get_intrinsics(self) -> Dict[str, float]:
        raise NotImplementedError

    def get_metadata(self) -> Dict:
        raise NotImplementedError
```

---

## adapters/upload\_source.py

```python
from __future__ import annotations
import cv2
from typing import Iterable, Dict
import numpy as np
from modal.utils import assert_portrait_and_fps

class UploadSource:
    def __init__(self, local_video_path: str, default_intrinsics=None):
        self.path = local_video_path
        self.default_intrinsics = default_intrinsics or {}

    def get_frames(self) -> Iterable[np.ndarray]:
        cap = cv2.VideoCapture(self.path)
        fps, W, H = assert_portrait_and_fps(cap)
        max_frames = fps * 10
        i = 0
        while i < max_frames:
            ok, fr = cap.read()
            if not ok:
                break
            yield fr
            i += 1
        cap.release()

    def get_intrinsics(self) -> Dict[str, float]:
        # Defaults for debug; iOS will provide real values later
        cap = cv2.VideoCapture(self.path)
        _, W, H = assert_portrait_and_fps(cap)
        cap.release()
        fx = float(self.default_intrinsics.get("fx", 1190.0))
        fy = float(self.default_intrinsics.get("fy", 1190.0))
        cx = float(self.default_intrinsics.get("cx", W / 2.0))
        cy = float(self.default_intrinsics.get("cy", H / 2.0))
        return {"fx": fx, "fy": fy, "cx": cx, "cy": cy}

    def get_metadata(self) -> Dict:
        cap = cv2.VideoCapture(self.path)
        fps, W, H = assert_portrait_and_fps(cap)
        cap.release()
        return {"fps": fps, "width": W, "height": H, "orientation": "portrait"}
```

---

## adapters/ios\_source.py (stub)

```python
class IosSource:
    def __init__(self, *_, **__):
        raise NotImplementedError("iOS ARKit adapter will be implemented in Phase 2.")
```

---

## edge-functions/common.ts

```ts
// Shared helpers for edge functions
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

export const supabaseAdmin = () => {
  const url = Deno.env.get("SUPABASE_URL")!;
  const key = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;
  return createClient(url, key, { auth: { persistSession: false } });
};

export type QueuePayload = {
  video_id: string;
  view: "side" | "rear";
  s3_key: string;
  intrinsics?: { fx: number; fy: number; cx: number; cy: number };
  height_cm?: number;
};
```

---

## edge-functions/queue\_video\_job/index.ts

```ts
// deno-lint-ignore-file no-explicit-any
import { supabaseAdmin, QueuePayload } from "../common.ts";

export async function handle(req: Request): Promise<Response> {
  try {
    const body = (await req.json()) as QueuePayload;
    if (!body.video_id || !body.view || !body.s3_key) {
      return new Response(JSON.stringify({ error: "Missing required fields" }), { status: 400 });
    }

    const sb = supabaseAdmin();

    // Upsert analysis holder (optional)
    const { data: analysis } = await sb
      .from("bio_run_analysis")
      .upsert({ id: body.video_id, status: "queued" }, { onConflict: "id" })
      .select()
      .single();

    const payload = {
      s3_bucket: Deno.env.get("S3_BUCKET")!,
      s3_key: body.s3_key,
      view: body.view,
      job_uuid: crypto.randomUUID(),
      intrinsics: body.intrinsics,
      height_cm: body.height_cm ?? 178,
    };

    // Insert queue row
    await sb.from("bio_modal_processing_queue").insert({
      video_id: body.video_id,
      view_type: body.view,
      status: "queued",
      modal_function_name: "process_running_video",
      modal_request_payload: payload,
      priority: 5,
    });

    // TODO: invoke Modal via HTTPS webhook or polling worker

    return new Response(JSON.stringify({ ok: true, analysis, payload }), { status: 200 });
  } catch (e) {
    return new Response(JSON.stringify({ error: String(e?.message ?? e) }), { status: 500 });
  }
}

export default handle;
```

---

## api/init\_upload\_session.ts (Next.js route stub)

```ts
// /api/init_upload_session.ts
import { NextRequest, NextResponse } from "next/server";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { createPresignedPost } from "@aws-sdk/s3-presigned-post";

export async function POST(req: NextRequest) {
  const bucket = process.env.S3_BUCKET!;
  const uuid = crypto.randomUUID();
  const key = `uploads/${uuid}/original.mp4`;

  const s3 = new S3Client({ region: process.env.AWS_REGION });
  const { url, fields } = await createPresignedPost(s3, {
    Bucket: bucket,
    Key: key,
    Conditions: [["content-length-range", 1, 600000000]],
    Expires: 600,
  });

  const intrinsics = { fx: 1190, fy: 1190, cx: 540, cy: 960 }; // portrait defaults
  return NextResponse.json({ video_id: uuid, s3_bucket: bucket, s3_key: key, url, fields, intrinsics });
}
```

---

## config/pipeline.json

```json
{
  "bbox_jitter_px": 2,
  "target_input": { "w": 256, "h": 192 },
  "max_seconds": 10,
  "fps_allowed": [30, 60],
  "portrait_sizes": [[1080, 1920], [2160, 3840]]
}
```

---

## config/logging.yaml

```yaml
version: 1
formatters:
  simple:
    format: "%(asctime)s %(levelname)s %(message)s"
handlers:
  console:
    class: logging.StreamHandler
    formatter: simple
    level: INFO
root:
  level: INFO
  handlers: [console]
```

---

## scripts/local\_ffprobe.sh

```bash
#!/usr/bin/env bash
set -euo pipefail
ffprobe -hide_banner -v error -show_entries stream=width,height,avg_frame_rate -of default=noprint_wrappers=1:nokey=1 "$1"
```

---

## scripts/make\_manifest.py

```python
import json, sys, hashlib, os

mp4, json_path = sys.argv[1], sys.argv[2]
manifest = {
  "mp4_size": os.path.getsize(mp4),
  "json_size": os.path.getsize(json_path),
  "mp4_sha256": hashlib.sha256(open(mp4,'rb').read()).hexdigest(),
  "json_sha256": hashlib.sha256(open(json_path,'rb').read()).hexdigest(),
}
print(json.dumps(manifest, indent=2))
```

---

## tests/unit/test\_one\_euro.py

```python
from modal.one_euro import OneEuroFilter

def test_filter_runs():
    f = OneEuroFilter(freq=60)
    vals = [f(x) for x in [0,1,2,3,4,5]]
    assert len(vals) == 6
```

---

## tests/unit/test\_coordinate\_ledger.py

```python
import json

def test_mm_to_m_single_conversion():
    # sample frame ledger
    mm = 1000.0
    m = mm / 1000.0
    assert abs(m - 1.0) < 1e-6
```

---

## tests/e2e/test\_sample\_video.py

```python
# Placeholder E2E: ensure portrait constraints logic works
import cv2
from modal.utils import assert_portrait_and_fps

# Provide a small portrait sample file before running this test

def test_asserts(tmp_path):
    # this test will be marked xfail until a sample file is added
    pass
```

---

## supabase/sql/permissions.sql

```sql
-- Example RLS or grants; adjust to match your current policies
-- Ensure service role can read/write all, anon can only read artifact URLs
```

---

## README.md

```markdown
# BodyPose3DNet Modal Pipeline (Scaffold)

This directory provides a GPU pipeline that takes **portrait** treadmill videos (1080×1920 or 2160×3840; **30 or 60 FPS only**), runs BodyPose3DNet on Modal, overlays a skeleton, and writes artifacts to S3.

## Requirements
- Modal account + GPU access
- S3 bucket and creds in env
- ONNX model in S3 at `BODYPOSE3DNET_MODEL_S3_KEY`

## Environment
```

AWS\_REGION=us-east-1 AWS\_ACCESS\_KEY\_ID=... AWS\_SECRET\_ACCESS\_KEY=... S3\_BUCKET=maxwattz-videos SUPABASE\_URL=... SUPABASE\_SERVICE\_ROLE\_KEY=... MODAL\_TOKEN\_ID=... MODAL\_TOKEN\_SECRET=... BODYPOSE3DNET\_MODEL\_S3\_KEY=models/bodypose3dnet/BodyPose3DNet\_deployable\_accuracy\_v1.0.onnx SKELETON\_JSON\_PATH=modal/skeleton\_34.json INPUT\_SOURCE=upload

````

## Deploy
```bash
modal deploy modal/processing_stub.py
````

## Run (local image sanity)

```bash
python -m modal.runner modal/processing_stub.py --local
```

## Web UI embedding (responsive portrait box)

Use a fixed **canvas box** with 9:16 aspect ratio, preserving proportions for overlay videos:

```html
<div class="video-box">
  <video src="/path/to/annotated.mp4" controls playsinline></video>
</div>
```

```css
.video-box { width: 100%; max-width: 420px; aspect-ratio: 9 / 16; }
.video-box video { width: 100%; height: 100%; object-fit: contain; background: #000; }
@media (min-width: 1200px) { .video-box { max-width: 540px; } }
```

- **object-fit: contain** preserves the portrait video inside its box, scaling with the page.
- For 4K (2160×3840) files the browser will downscale automatically but keep sharpness on retina displays.

## Notes

- The overlay drawing uses `modal/skeleton_34.json`. Replace this file with the **official topology** when ready.
- If the ONNX model exposes `pose2d` in crop coords, overlays will use it. If not, you’ll see a placeholder bbox with a warning.
- FPS and resolution are **enforced**: videos outside {30,60} FPS or non-portrait sizes will be rejected.

```
```
