"""
Database integration tests for BodyPose3DNet Modal Pipeline
Tests the database schema and operations used by edge functions
"""

import pytest
import json
from typing import Dict, Any
from uuid import uuid4


# Mock Supabase client for testing
class MockSupabaseClient:
    def __init__(self):
        self.bio_run_analysis = []
        self.bio_modal_processing_queue = []
    
    def from_(self, table_name: str):
        return MockTable(table_name, self)
    
    def get_table_data(self, table_name: str):
        if table_name == "bio_run_analysis":
            return self.bio_run_analysis
        elif table_name == "bio_modal_processing_queue":
            return self.bio_modal_processing_queue
        return []


class MockTable:
    def __init__(self, table_name: str, client: MockSupabaseClient):
        self.table_name = table_name
        self.client = client
        self.operations = []
    
    def upsert(self, data: Dict[str, Any], **kwargs):
        # Simulate upsert operation
        table_data = self.client.get_table_data(self.table_name)
        
        if isinstance(data, dict):
            data_list = [data]
        else:
            data_list = data
            
        for item in data_list:
            # Add default fields
            if 'id' not in item:
                item['id'] = str(uuid4())
            if 'created_at' not in item:
                item['created_at'] = '2023-01-01T00:00:00Z'
            
            # Find existing record or add new
            existing_idx = None
            for i, existing in enumerate(table_data):
                if existing.get('id') == item.get('id'):
                    existing_idx = i
                    break
            
            if existing_idx is not None:
                table_data[existing_idx].update(item)
            else:
                table_data.append(item.copy())
                
        return self
    
    def insert(self, data: Dict[str, Any]):
        # Simulate insert operation
        table_data = self.client.get_table_data(self.table_name)
        
        if isinstance(data, dict):
            data_list = [data]
        else:
            data_list = data
            
        for item in data_list:
            if 'id' not in item:
                item['id'] = str(uuid4())
            if 'created_at' not in item:
                item['created_at'] = '2023-01-01T00:00:00Z'
            table_data.append(item.copy())
        
        return self
    
    def update(self, data: Dict[str, Any]):
        self.update_data = data
        return self
    
    def eq(self, column: str, value: Any):
        # Simulate WHERE clause
        table_data = self.client.get_table_data(self.table_name)
        
        if hasattr(self, 'update_data'):
            # Apply update to matching records
            for item in table_data:
                if item.get(column) == value:
                    item.update(self.update_data)
        
        return self
    
    def select(self):
        return self
    
    def single(self):
        # Return first matching record
        table_data = self.client.get_table_data(self.table_name)
        if table_data:
            return {"data": table_data[0], "error": None}
        return {"data": None, "error": "No data found"}


def test_queue_video_job_database_operations():
    """Test the database operations used in queue_video_job edge function"""
    
    # Mock Supabase client
    sb = MockSupabaseClient()
    
    # Simulate queue_video_job edge function operations
    video_id = str(uuid4())
    job_uuid = str(uuid4())
    
    # Test analysis record upsert
    analysis_data = {"id": video_id, "status": "queued"}
    result = sb.from_("bio_run_analysis").upsert(analysis_data, onConflict="id").select().single()
    
    assert result["data"] is not None
    assert result["data"]["status"] == "queued"
    assert len(sb.bio_run_analysis) == 1
    
    # Test queue insertion
    payload = {
        "s3_bucket": "test-bucket",
        "s3_key": f"uploads/{video_id}/original.mp4", 
        "view": "side",
        "job_uuid": job_uuid,
        "intrinsics": {"fx": 1190.0, "fy": 1190.0, "cx": 540.0, "cy": 960.0},
        "height_cm": 178,
    }
    
    queue_data = {
        "video_id": video_id,
        "view_type": "side",
        "status": "queued",
        "modal_function_name": "process_running_video",
        "modal_request_payload": payload,
        "priority": 5,
    }
    
    sb.from_("bio_modal_processing_queue").insert(queue_data)
    
    assert len(sb.bio_modal_processing_queue) == 1
    queue_record = sb.bio_modal_processing_queue[0]
    assert queue_record["video_id"] == video_id
    assert queue_record["status"] == "queued"
    assert queue_record["modal_request_payload"]["job_uuid"] == job_uuid


def test_modal_completion_webhook_database_operations():
    """Test the database operations used in modal_completion_webhook"""
    
    sb = MockSupabaseClient()
    
    # Setup initial data
    video_id = str(uuid4())
    job_uuid = str(uuid4())
    
    # Initial analysis record
    sb.from_("bio_run_analysis").upsert({
        "id": video_id,
        "status": "processing",
        "modal_job_id": job_uuid
    })
    
    # Initial queue record  
    sb.from_("bio_modal_processing_queue").insert({
        "video_id": video_id,
        "status": "processing",
        "modal_request_payload": {"job_uuid": job_uuid}
    })
    
    # Simulate successful completion
    artifacts = {
        "annotated_mp4": f"s3://bucket/running-metrics-side/{job_uuid}/annotated.mp4",
        "keypoints_3d_json": f"s3://bucket/running-metrics-side/{job_uuid}/keypoints_3d.json",
    }
    
    # Update analysis record
    sb.from_("bio_run_analysis").update({
        "status": "completed",
        "annotated_mp4_url": artifacts["annotated_mp4"],
        "keypoints_json_url": artifacts["keypoints_3d_json"], 
        "completed_at": "2023-01-01T01:00:00Z",
        "view_type": "side",
    }).eq("modal_job_id", job_uuid)
    
    # Update queue record
    sb.from_("bio_modal_processing_queue").update({
        "status": "completed",
        "completed_at": "2023-01-01T01:00:00Z",
        "modal_response": {"status": "ok", "artifacts": artifacts},
    }).eq("modal_request_payload->job_uuid", job_uuid)  # JSONB query simulation
    
    # Verify updates
    analysis = sb.bio_run_analysis[0]
    assert analysis["status"] == "completed"
    assert analysis["annotated_mp4_url"] == artifacts["annotated_mp4"]
    assert "completed_at" in analysis
    
    queue_record = sb.bio_modal_processing_queue[0]
    assert queue_record["status"] == "completed"
    assert "completed_at" in queue_record


def test_database_schema_constraints():
    """Test that database constraints are properly defined"""
    
    sb = MockSupabaseClient()
    
    # Test valid status values (would be enforced by CHECK constraints)
    valid_statuses = ["queued", "processing", "completed", "failed"]
    
    for status in valid_statuses:
        # Should not raise error
        data = {"status": status, "modal_job_id": str(uuid4())}
        sb.from_("bio_run_analysis").upsert(data)
    
    # Test valid view types
    valid_views = ["side", "rear"]
    
    for view in valid_views:
        # Should not raise error  
        data = {
            "video_id": str(uuid4()),
            "view_type": view,
            "modal_request_payload": {"test": "data"}
        }
        sb.from_("bio_modal_processing_queue").insert(data)
    
    assert len(sb.bio_run_analysis) == len(valid_statuses)
    assert len(sb.bio_modal_processing_queue) == len(valid_views)


def test_jsonb_payload_structure():
    """Test that JSONB payload structure matches expectations"""
    
    # Test modal request payload structure
    payload = {
        "s3_bucket": "test-bucket",
        "s3_key": "uploads/123/original.mp4",
        "view": "side", 
        "job_uuid": str(uuid4()),
        "intrinsics": {
            "fx": 1190.0,
            "fy": 1190.0, 
            "cx": 540.0,
            "cy": 960.0
        },
        "height_cm": 178,
    }
    
    # Verify JSON serializable
    json_payload = json.dumps(payload)
    parsed_payload = json.loads(json_payload)
    
    assert parsed_payload["s3_bucket"] == "test-bucket"
    assert parsed_payload["view"] == "side"
    assert parsed_payload["intrinsics"]["fx"] == 1190.0
    assert parsed_payload["height_cm"] == 178
    
    # Test modal response payload structure
    response = {
        "status": "ok",
        "uuid": str(uuid4()),
        "view": "side",
        "artifacts": {
            "annotated_mp4": "s3://bucket/path/annotated.mp4",
            "keypoints_3d_json": "s3://bucket/path/keypoints_3d.json"
        }
    }
    
    # Verify JSON serializable
    json_response = json.dumps(response)
    parsed_response = json.loads(json_response)
    
    assert parsed_response["status"] == "ok"
    assert "artifacts" in parsed_response
    assert "annotated_mp4" in parsed_response["artifacts"]


if __name__ == "__main__":
    pytest.main([__file__])