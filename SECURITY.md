# Security Guidelines for React SDK

## 🚨 CRITICAL: Environment Configuration Security

### ❌ NEVER DO THIS:
- Put real credentials directly in HTML, JavaScript, or TypeScript files
- Commit environment files with real credentials to version control
- Share credentials in plain text via email, chat, or documentation

### ✅ SECURE PRACTICES:

#### 1. Local Development Environment Setup

1. **Copy the template:**
   ```bash
   cp env.template.js env.local.js
   ```

2. **Edit `env.local.js` with your real credentials:**
   ```javascript
   window.ENV = {
       SUPABASE_URL: 'https://your-actual-project.supabase.co',
       SUPABASE_ANON_KEY: 'your-real-anon-key-here',
       AWS_ACCESS_KEY_ID: 'AKIA...your-real-key',
       // ... other real values
   };
   ```

3. **Verify `.gitignore` excludes your credentials:**
   ```
   env.local.js
   .env
   .env.local
   ```

#### 2. Production Environment

For production deployments:

- **Use environment variables** from your hosting platform
- **Use AWS IAM roles** instead of access keys when possible
- **Rotate credentials** regularly
- **Use least-privilege access** policies

#### 3. Testing Files

The test files (`test-*.html`) are designed to:

- ✅ Load secure credentials from `env.local.js` if available
- ✅ Fall back to safe mock mode if no credentials found
- ✅ Never expose real credentials in the source code
- ✅ Provide clear warnings about security

### File Security Status:

| File | Status | Description |
|------|--------|-------------|
| `env.template.js` | ✅ SAFE | Template with placeholder values only |
| `env.local.js` | 🔒 SECURE | Your real credentials (git-ignored) |
| `test-*.html` | ✅ SAFE | No credentials, loads from env.local.js |

### Security Checklist:

- [ ] `env.local.js` is in `.gitignore`
- [ ] No real credentials in tracked files
- [ ] Test files load environment securely
- [ ] AWS credentials have minimal required permissions
- [ ] Supabase RLS policies are properly configured
- [ ] Regular credential rotation schedule in place

### Emergency Response:

If credentials are accidentally committed:

1. **Immediately rotate/revoke** the exposed credentials
2. **Remove from git history** using `git filter-branch` or BFG
3. **Force push** the cleaned history
4. **Audit access logs** for unauthorized usage
5. **Update all systems** with new credentials

### Questions?

If you have security concerns or questions, please:
- Review this documentation
- Check with your security team
- Use secure channels for credential-related discussions

Remember: **Security is everyone's responsibility!** 🔐