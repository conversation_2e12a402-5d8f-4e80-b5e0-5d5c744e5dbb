## PRD — NVIDIA BodyPose3DNet on Modal (with iOS LiDAR), Side + Rear Treadmill Views

## Global goals & constraints

* **Goal:** produce a **smooth, annotated video** (skeleton overlay) + **timestamped 3D joint timeseries** + **running metrics** for each 10-sec clip.
* **Model:** `BodyPose3DNet_deployable_accuracy_v1.0.onnx` (accuracy build). Outputs 3D joints in **mm**; we report **meters**. Require ≥95% correct joint detection in controlled tests.
* **Modal env:** CUDA 11.8 base, onnxruntime-gpu, OpenCV, numpy, ffmpeg, boto3; cold start <5s; per-frame inference <30ms.
* **Input video:** 30 or 60 FPS, 1080×1920 or 2160×3840 portrait; model input is **256×192 (4:3)** via non-distorting crop.
* **People detection:** NVIDIA PeopleNet; detect **every frame**; jitter <2 px; single subject.
* **Intrinsics:** capture ARKit `fx, fy, cx, cy` on iOS and send with upload.
* **Scaling:** use NVIDIA limb defaults; optionally scale by user height (cm).
* **Inference:** ONNXRuntime-GPU, **per-frame** (not batch), outputs **(34, 3)** in mm.
* **Smoothing:** One-Euro filter per joint across time.
* **Outputs:** `annotated.mp4` (original FPS/res), `keypoints_3d.json` (meters + confidence), `metrics.json`. Upload under per-clip **UUID** path in S3.

---

## Coordinate Ledger (never drift units again)

| Stage               | Space / Units           | Source of Truth         | Stored As                    |
| ------------------- | ----------------------- | ----------------------- | ---------------------------- |
| PeopleNet bboxes    | **Pixels (orig frame)** | Phase 2 Step 1          | `detections[i].bbox_px`      |
| Cropped model input | **Pixels (256×192)**    | Phase 2 Step 2          | `preproc[i].crop_px_256x192` |
| Camera intrinsics   | **fx,fy,cx,cy**         | iOS ARKit               | `upload.intrinsics`          |
| Model output raw    | **mm (3D)**             | BodyPose3DNet           | `raw3d[i][j].mm`             |
| Smoothed 3D         | **mm → meters**         | One-Euro                | `smooth3d[i][j].m`           |
| Overlay 2D          | **Pixels (orig frame)** | `pose2d_org_img`        | drawn onto original frames   |
| Metrics             | **Meters**              | computed post-smoothing | `metrics.json`               |

**Unit tests (must pass in CI):**

* Assert **only** one conversion mm→m happens (exactly once) before serialization.
* Assert `overlay.width/height` equals **original** frame dims; FPS preserved.

---

## Data contracts

### 1) Upload request (iOS → API)

```json
{
  "video_id": "UUIDv4",
  "s3_key": "uploads/UUID/original.mp4",
  "view": "side|rear",
  "fps_hint": 30,
  "intrinsics": {"fx": 1190.23, "fy": 1189.77, "cx": 960.0, "cy": 540.0},
  "user_height_cm": 180
}
```

(ARKit intrinsics are required; height optional.)

### 2) Modal job input

```json
{
  "s3_bucket": "maxwattz-videos",
  "s3_key": "uploads/UUID/original.mp4",
  "intrinsics": {...},
  "height_cm": 180,
  "job_uuid": "UUIDv4",
  "view": "side"
}
```

### 3) Outputs (paths)

```
s3://maxwattz-videos/running-metrics-{view}/{UUID}/
  ├── annotated.mp4
  ├── keypoints_3d.json
  └── metrics.json
```

(Strict UUID naming; signed URL access.)

---

## Parallelization plan (Gemini CLI orchestration)

**Safe to run concurrently:**

1. **Fetch + decode** frames from S3 **while** precomputing PeopleNet bboxes (producer/consumer).
2. **Cropping/resizing** in a worker pool as soon as bboxes arrive for each frame.
3. **One-Euro smoothing** can stream while inference proceeds (apply when each new frame’s 3D result arrives).
4. **Overlay video encoding** can run as soon as `pose2d_org_img` for a frame is ready; write frames to VideoWriter sequentially.
5. **Metric computation** can begin once enough stride cycles are detected from the right ankle series (windowed).

> Keep ONNX inference **per-frame** as required; don’t micro-batch.

---

## Phase 1 — Model & GPU Environment (done once per deploy)

**Locks & steps** (Dockerfile, Modal stub, CUDA provider check) are exactly as defined earlier; copy as-is.

**Deliverables**

* Docker image with pinned deps.
* `MODEL_CHECKSUMS.md` with sha256 of the ONNX file.
* Modal function `run_inference` scaffold created.

**Acceptance tests**

* `onnxruntime.get_providers()` includes `CUDAExecutionProvider`.
* Cold start & per-frame timing meet targets.

---

## Phase 2 — Preprocessing & Frame Prep

1. **Detect person every frame** with PeopleNet → highest-confidence full-body box; record jitter and reject runs >2px median jitter.
2. **Center-crop** portrait frames to 4:3; **resize** to 256×192; record mapping to recover 2D overlays later.
3. **Smooth bboxes** over 3–5 frames (moving average).

**Outputs**

* `preproc/frames_256x192/` (RAM or tempfs)
* `preproc/bbox_smoothed.json`
* `metadata.json` with original dims, fps, crops.

**Acceptance tests**

* FPS ∈ {30,60}.
* Jitter <2px between frames.

---

## Phase 3 — Camera Calibration & Scale

1. **ARKit intrinsics**: collect `fx, fy, cx, cy` on-device; pass with upload.
2. **Limb defaults**: load NVIDIA `mean_limb_lengths`, `scale_normalized` JSON.
3. **Optional per-user scale** by height (cm).

**Outputs**

* `intrinsics.json`, `limb_lengths.json`, `scale_multiplier`.

**Acceptance tests**

* If height provided, lengths scaled by `height_cm/175`.

---

## Phase 4 — Inference Pipeline on Modal

1. **Download from S3** using Modal secret creds.
2. **Decode frames** → apply Phase 2; limit length to ≤10s.
3. **Run ONNX** per frame: shape `(1,3,256,192)`; output `(1,34,3)` in **mm**.
4. **Smooth** each joint (x,y,z) with One-Euro, then **convert to meters** **once**.
5. **Save**: (a) skeleton overlay on original frames using `pose2d_org_img` and VideoWriter, (b) 3D JSON series with confidence.

**Outputs**

* Smoothed 3D keypoints (meters), `out.mp4`, `output.json`.

**Acceptance tests**

* Overlay matches original resolution & FPS.
* JSON timestamps equal `i/fps`.

---

## Phase 5 — Post-Processing & Visualization

1. **Metrics** (meters): cadence, GCT, stride length, vertical osc., step symmetry. Use ankle Z (or Y per axis convention) to find foot strikes; compute cadence and other measures; write `metrics.json`.
2. **Overlay video**: draw joints & edges; **preserve original FPS/res**; keep left/right coloring consistent. Write `annotated.mp4`.
3. **Timeseries JSON**: per-frame `{timestamp, joint, x, y, z, confidence}` in **meters**.
4. **Upload** all three artifacts under UUID path in S3.

**Acceptance tests**

* Three artifacts present and downloadable via signed URLs.

---

## Skeleton + file schemas

### `SKELETON_CONNECTIONS` (excerpt)

> Keep using the official BodyPose3DNet 34-keypoint topology from the model card; store as `config/skeleton_34.json`. (ClaudeCode: load this at runtime so UI coloring is consistent with Phase 5 Step 2.)

### `keypoints_3d.json` (per frame)

```json
[
  {
    "timestamp": 0.033,
    "joints": [
      {"name":"left_hip","x":0.012,"y":0.893,"z":-0.021,"confidence":0.98}
    ]
  }
]
```

(Structure mirrors the example in Phase 4.)

### `metrics.json`

```json
{
  "fps": 60,
  "cadence_spm": 174.2,
  "ground_contact_time_ms": {"left": 242, "right": 238},
  "stride_length_m": 1.47,
  "vertical_oscillation_cm": 6.1,
  "step_symmetry_pct": 49.6
}
```

(Metrics list and timing derived from Phase 5 Step 1.)

---

## Logging & observability

* **Per-frame log**: `fps`, bbox jitter, crop window, inference ms, smoothing ms.
* **Per-job log**: total frames, dropped frames, mm→m conversion count (should be 1), output sizes.
* **Artifacts manifest** written as `manifest.json` beside outputs.

---

## Capture guidelines (side & rear examples)

* Side and rear views shot \~5 ft away, treadmill centered, full body in frame (no limb occlusion). Use your “SuccessfulPose” images as visual QA reference before upload.

---

## Ready-to-paste implementation snippets (ClaudeCode can expand)

**Modal function scaffold**

```python
# modal_functions/process_running_video.py
import modal
stub = modal.Stub("process_running_video")

image = modal.Image.from_dockerfile("./Dockerfile")
@stub.function(gpu="any", image=image, timeout=600)
def run_inference(payload: dict) -> dict:
    # 1) download S3  2) decode frames  3) detect/crop/resize
    # 4) onnx per-frame  5) smooth -> meters  6) overlay+json
    # 7) metrics        8) upload artifacts
    return {"status":"ok","uuid":payload["job_uuid"]}
```

(Dockerfile + provider checks per Phase 1.)

**Download from S3**

```python
def download_from_s3(bucket, key):
    import boto3, tempfile
    s3 = boto3.client('s3')
    tmp = tempfile.NamedTemporaryFile(delete=False)
    s3.download_fileobj(bucket, key, tmp)
    return tmp.name
```

**Decode frames**

```python
cap = cv2.VideoCapture(local_path)
fps = int(cap.get(cv2.CAP_PROP_FPS))
assert fps in (30,60)
frames=[]
while True:
    ok, f = cap.read()
    if not ok: break
    frames.append(f)
```

**ONNX inference (per frame)**

```python
sess = ort.InferenceSession(MODEL_PATH, providers=['CUDAExecutionProvider'])
tensor = np.expand_dims(frame_256x192, 0).astype(np.float32)
pose3d_mm = sess.run(None, {"input": tensor})[0][0]   # (34,3) mm
```

**One-Euro smoothing (streaming)**

```python
filters = [[OneEuroFilter(freq=fps) for _ in range(3)] for _ in range(34)]
def smooth_mm_to_m(prev, cur_mm):
    out=[]
    for j,(x,y,z) in enumerate(cur_mm):
        sx = filters[j][0](x); sy = filters[j][1](y); sz = filters[j][2](z)
        out.append([sx/1000.0, sy/1000.0, sz/1000.0]) # single mm->m
    return out
```

**Overlay & writer**

```python
writer = cv2.VideoWriter("annotated.mp4",
                         cv2.VideoWriter_fourcc(*'mp4v'),
                         fps, (orig_w, orig_h))
# draw lines/circles using pose2d_org_img mapped into original coords
```

**Export timeseries**

```python
for i, frame in enumerate(smoothed_keypoints_m):
    ts = i / fps
    # serialize as shown in schema
```

**Upload outputs**

```python
s3.upload_file("metrics.json", bucket, f"running-metrics-{view}/{uuid}/metrics.json")
s3.upload_file("keypoints_3d.json", bucket, f"running-metrics-{view}/{uuid}/keypoints_3d.json")
s3.upload_file("annotated.mp4", bucket, f"running-metrics-{view}/{uuid}/annotated.mp4")
```

---

## QA checklist (ClaudeCode can automate)

* [ ] Side & rear clips each produce 3 artifacts; signed URLs valid.
* [ ] Overlay video equals original FPS & resolution.
* [ ] `keypoints_3d.json` values are in **meters** (max abs value < 3.0).
* [ ] Bounding-box jitter report <2 px median.
* [ ] Cold start <5s; per-frame <30ms on GPU instance.
