# COPY THIS FILE TO .env.local AND FILL IN YOUR ACTUAL VALUES
# DO NOT COMMIT .env.local TO VERSION CONTROL

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key-here
NEXT_PUBLIC_SUPABASE_SERVICE_KEY=your-service-key

# AWS Configuration (for S3, etc.)
AWS_ACCESS_KEY_ID=AKIA...your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-access-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-bucket-name
AWS_S3_SIDE_PREFIX=maxwattz-running-videos-raw-side/
AWS_S3_REAR_PREFIX=maxwattz-running-videos-raw-rear/
AWS_S3_METRICS_SIDE_PREFIX=maxwattz-running-metrics-side/
AWS_S3_METRICS_REAR_PREFIX=maxwattz-running-metrics-rear/

# AI API Keys
OPENAI_API_KEY=sk-...your-openai-key
ANTHROPIC_API_KEY=sk-ant-...your-anthropic-key
XAI_API_KEY=xai-...your-grok-key

# Peak Insight SDK (for Pose Analysis)
NEXT_PUBLIC_PEAK_INSIGHT_API_KEY=your-peak-insight-key
NEXT_PUBLIC_PEAK_INSIGHT_API_URL=https://api.peakinsight.com

# Next.js Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Optional: Database URL (if using Prisma or direct DB connection)
DATABASE_URL=postgresql://user:password@localhost:5432/maxwattz

# NVIDIA API Configuration
NVIDIA_API_KEY=nvapi-your-access-key
NVIDIA_API_URL=https://api.nvidia.com
NVIDIA_KEY_ID=your-key-id