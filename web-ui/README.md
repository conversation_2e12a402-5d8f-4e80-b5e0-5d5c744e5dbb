# BodyPose3DNet Web UI Integration

This directory contains React components for integrating BodyPose3DNet results into web applications.

## Components

### BodyPose3DViewer

A React component that displays:
- **Video Player**: Shows the annotated MP4 with skeleton overlay
- **Status Tracking**: Real-time analysis status (queued, processing, completed, failed)
- **Processing Stats**: Frame rate, resolution, inference timing, stability metrics
- **3D Keypoints**: Download and preview JSON keypoint data
- **Error Handling**: Retry functionality for failed analyses

### Key Features

- **Portrait Video Optimized**: Handles 1080×1920 and 2160×3840 video properly
- **Mobile Responsive**: Works on mobile devices with proper aspect ratios
- **Real-time Updates**: Polls analysis status until completion
- **Artifact Display**: Shows processing stats from manifest.json
- **Download Support**: Direct download of keypoints JSON data

## Usage

```tsx
import { BodyPose3DViewer } from './components/BodyPose3DViewer';

function MyApp() {
  const [analysis, setAnalysis] = useState<Analysis>({
    id: "uuid-here",
    status: "completed",
    annotated_mp4_url: "s3://bucket/path/annotated.mp4",
    keypoints_json_url: "s3://bucket/path/keypoints_3d.json",
    manifest_json_url: "s3://bucket/path/manifest.json",
    view_type: "side",
    created_at: "2024-01-01T00:00:00Z",
    completed_at: "2024-01-01T00:01:00Z"
  });

  return (
    <BodyPose3DViewer 
      analysis={analysis}
      onRetry={() => retryAnalysis(analysis.id)}
    />
  );
}
```

## Integration with Supabase

The component expects analysis records matching the database schema:

```sql
-- Fetch analysis with artifacts
SELECT 
  id,
  status,
  annotated_mp4_url,
  keypoints_json_url, 
  manifest_json_url,
  view_type,
  error_message,
  created_at,
  completed_at
FROM bio_run_analysis 
WHERE id = $1;
```

## S3 URL Access

Ensure your S3 bucket has proper CORS configuration for web access:

```json
{
  "CORSRules": [
    {
      "AllowedOrigins": ["https://yourdomain.com"],
      "AllowedMethods": ["GET"],
      "AllowedHeaders": ["*"],
      "MaxAgeSeconds": 3600
    }
  ]
}
```

## Styling

Uses Tailwind CSS for styling. Key responsive breakpoints:
- **Mobile**: Single column layout
- **Desktop**: Video + stats sidebar layout
- **Portrait Videos**: Properly centered with max height constraints

## Real-time Updates

For live status updates, implement polling or WebSocket connections:

```tsx
useEffect(() => {
  const interval = setInterval(async () => {
    if (analysis.status === 'queued' || analysis.status === 'processing') {
      const updated = await fetchAnalysisStatus(analysis.id);
      setAnalysis(updated);
    }
  }, 2000);

  return () => clearInterval(interval);
}, [analysis.status]);
```