/**
 * BodyPose3DViewer - Web UI component for BodyPose3DNet results
 * Displays video with skeleton overlay and 3D keypoints
 */

import React, { useState, useRef, useEffect } from 'react';

interface Artifact {
  annotated_mp4: string;
  keypoints_3d_json: string;
  manifest_json?: string;
}

interface Analysis {
  id: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  annotated_mp4_url?: string;
  keypoints_json_url?: string;
  manifest_json_url?: string;
  view_type?: 'side' | 'rear';
  error_message?: string;
  created_at: string;
  completed_at?: string;
}

interface Props {
  analysis: Analysis;
  onRetry?: () => void;
}

export const BodyPose3DViewer: React.FC<Props> = ({ analysis, onRetry }) => {
  const [manifest, setManifest] = useState<any>(null);
  const [keypoints, setKeypoints] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Load manifest and keypoints when analysis completes
  useEffect(() => {
    if (analysis.status === 'completed' && analysis.manifest_json_url) {
      loadArtifacts();
    }
  }, [analysis.status, analysis.manifest_json_url]);

  const loadArtifacts = async () => {
    setLoading(true);
    try {
      // Load manifest
      if (analysis.manifest_json_url) {
        const manifestResponse = await fetch(analysis.manifest_json_url);
        const manifestData = await manifestResponse.json();
        setManifest(manifestData);
      }

      // Load keypoints
      if (analysis.keypoints_json_url) {
        const keypointsResponse = await fetch(analysis.keypoints_json_url);
        const keypointsData = await keypointsResponse.json();
        setKeypoints(keypointsData);
      }
    } catch (error) {
      console.error('Error loading artifacts:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (seconds: number) => {
    return `${(seconds / 1000).toFixed(1)}s`;
  };

  const renderStatus = () => {
    const statusColors = {
      queued: 'bg-yellow-100 text-yellow-800',
      processing: 'bg-blue-100 text-blue-800', 
      completed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800'
    };

    return (
      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[analysis.status]}`}>
        {analysis.status.charAt(0).toUpperCase() + analysis.status.slice(1)}
      </div>
    );
  };

  if (analysis.status === 'failed') {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium text-red-900">Processing Failed</h3>
            {renderStatus()}
          </div>
          <p className="text-red-700 mb-4">{analysis.error_message || 'Unknown error occurred'}</p>
          {onRetry && (
            <button
              onClick={onRetry}
              className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Retry Analysis
            </button>
          )}
        </div>
      </div>
    );
  }

  if (analysis.status !== 'completed') {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium text-blue-900">
              {analysis.status === 'queued' ? 'Video Queued' : 'Processing Video'}
            </h3>
            {renderStatus()}
          </div>
          <p className="text-blue-700">
            {analysis.status === 'queued' 
              ? 'Your video is in the processing queue...'
              : 'Analyzing 3D pose and generating skeleton overlay...'
            }
          </p>
          <div className="mt-4">
            <div className="animate-pulse bg-blue-200 h-2 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">3D Pose Analysis</h2>
          <p className="text-gray-600">View: {analysis.view_type} • {renderStatus()}</p>
        </div>
      </div>

      {/* Video Player - Portrait optimized */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <div className="lg:col-span-2">
          <div className="bg-gray-100 rounded-lg overflow-hidden">
            <h3 className="text-lg font-medium p-4 bg-gray-50 border-b">Annotated Video</h3>
            <div className="p-4 flex justify-center">
              {analysis.annotated_mp4_url ? (
                <video
                  ref={videoRef}
                  controls
                  className="max-w-full h-auto rounded shadow-lg"
                  style={{ maxHeight: '600px' }}
                  poster="/placeholder-poster.png"
                >
                  <source src={analysis.annotated_mp4_url} type="video/mp4" />
                  Your browser does not support video playback.
                </video>
              ) : (
                <div className="bg-gray-200 w-full h-96 rounded flex items-center justify-center">
                  <span className="text-gray-500">Video not available</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Stats Panel */}
        <div className="space-y-4">
          {manifest && (
            <div className="bg-white rounded-lg border p-4">
              <h3 className="text-lg font-medium mb-3">Processing Stats</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Resolution:</span>
                  <span>{manifest.width}×{manifest.height}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">FPS:</span>
                  <span>{manifest.fps}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Frames:</span>
                  <span>{manifest.durations?.frames_processed || 'N/A'}</span>
                </div>
                {manifest.durations?.inference && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg Inference:</span>
                    <span>{manifest.durations.inference.avg_ms}ms</span>
                  </div>
                )}
                {manifest.detection?.stability_score && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Stability Score:</span>
                    <span>{manifest.detection.stability_score.toFixed(3)}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {keypoints && (
            <div className="bg-white rounded-lg border p-4">
              <h3 className="text-lg font-medium mb-3">3D Keypoints</h3>
              <div className="text-sm text-gray-600 mb-2">
                {keypoints.length} frames • 34 joints per frame
              </div>
              <div className="max-h-32 overflow-y-auto text-xs">
                {keypoints.slice(0, 5).map((frame: any, idx: number) => (
                  <div key={idx} className="mb-1">
                    <strong>Frame {idx}:</strong> {frame.joints.length} joints
                  </div>
                ))}
                {keypoints.length > 5 && (
                  <div className="text-gray-500">... and {keypoints.length - 5} more frames</div>
                )}
              </div>
              <button 
                onClick={() => {
                  const blob = new Blob([JSON.stringify(keypoints, null, 2)], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `keypoints_${analysis.id}.json`;
                  a.click();
                }}
                className="mt-3 w-full bg-blue-600 text-white text-sm px-3 py-2 rounded hover:bg-blue-700"
              >
                Download Keypoints JSON
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">Loading artifacts...</p>
        </div>
      )}

      {/* Analysis Details */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-lg font-medium mb-3">Analysis Details</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Analysis ID:</span>
            <div className="font-mono text-xs">{analysis.id}</div>
          </div>
          <div>
            <span className="text-gray-600">Created:</span>
            <div>{new Date(analysis.created_at).toLocaleString()}</div>
          </div>
          {analysis.completed_at && (
            <div>
              <span className="text-gray-600">Completed:</span>
              <div>{new Date(analysis.completed_at).toLocaleString()}</div>
            </div>
          )}
          {manifest?.durations?.total_sec && (
            <div>
              <span className="text-gray-600">Processing Time:</span>
              <div>{manifest.durations.total_sec.toFixed(1)}s</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};