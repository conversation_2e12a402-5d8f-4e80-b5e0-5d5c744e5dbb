#!/usr/bin/env python3
"""
Real Modal Processing Test - Actually process Michael_test_2.MOV through Modal
This will test the complete pipeline end-to-end with GPU processing
"""

import json
import time
from uuid import uuid4
import requests
import os

def test_modal_processing():
    """Test actual Modal processing with Michael_test_2.MOV"""
    
    print("🎬 Testing BodyPose3DNet Modal Processing with Michael_test_2.MOV")
    print("=" * 70)
    
    # Create test payload
    job_uuid = str(uuid4())
    test_payload = {
        "s3_bucket": "maxwattz-videos",
        "s3_key": "maxwattz-running-videos-raw-side/Michael_test_2.MOV",
        "view": "side",
        "job_uuid": job_uuid,
        "intrinsics": {"fx": 1190.0, "fy": 1190.0, "cx": 540.0, "cy": 960.0},
        "height_cm": 178,
    }
    
    print(f"📋 Test Parameters:")
    print(f"   Job UUID: {job_uuid}")
    print(f"   S3 Video: s3://{test_payload['s3_bucket']}/{test_payload['s3_key']}")
    print(f"   View: {test_payload['view']}")
    print(f"   Height: {test_payload['height_cm']}cm")
    
    # Expected output artifacts
    expected_artifacts = {
        "annotated_mp4": f"s3://{test_payload['s3_bucket']}/running-metrics-{test_payload['view']}/{job_uuid}/annotated.mp4",
        "keypoints_3d_json": f"s3://{test_payload['s3_bucket']}/running-metrics-{test_payload['view']}/{job_uuid}/keypoints_3d.json",
        "manifest_json": f"s3://{test_payload['s3_bucket']}/running-metrics-{test_payload['view']}/{job_uuid}/manifest.json"
    }
    
    print(f"\n📦 Expected Output Artifacts:")
    for name, url in expected_artifacts.items():
        print(f"   {name}: {url}")
    
    print(f"\n🚀 Invoking Modal function...")
    print(f"   This will actually process the video with GPU inference")
    
    try:
        # Import the Modal app and call the function directly
        import sys
        import os
        sys.path.insert(0, os.path.join(os.getcwd(), 'modal'))
        
        import modal
        
        print(f"   ✅ Connecting to deployed Modal function")
        print(f"   🔥 Starting GPU processing...")
        
        start_time = time.time()
        
        # Call the deployed Modal function directly
        f = modal.Function.from_name("process_running_video", "run_inference")
        
        # Call the function
        result = f.remote(test_payload)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"   ✅ Processing completed in {processing_time:.1f}s")
        print(f"\n📊 Results:")
        print(f"   Status: {result.get('status', 'unknown')}")
        print(f"   UUID: {result.get('uuid', 'missing')}")
        print(f"   View: {result.get('view', 'missing')}")
        
        if result.get('artifacts'):
            print(f"   🎯 Generated Artifacts:")
            for name, url in result['artifacts'].items():
                print(f"      {name}: {url}")
        
        # Verify artifacts match expected
        if result.get('artifacts') == expected_artifacts:
            print(f"   ✅ Artifact URLs match expected format")
        else:
            print(f"   ⚠️  Artifact URLs differ from expected")
        
        print(f"\n🎉 SUCCESS: Modal processing completed!")
        print(f"   📹 Annotated video: {result['artifacts']['annotated_mp4']}")
        print(f"   📊 3D keypoints: {result['artifacts']['keypoints_3d_json']}")
        print(f"   📋 Manifest: {result['artifacts']['manifest_json']}")
        
        return result
        
    except Exception as e:
        print(f"   ❌ Modal processing failed: {e}")
        print(f"\n💡 Troubleshooting:")
        print(f"   1. Ensure Modal is deployed: modal deploy modal/processing_stub.py")
        print(f"   2. Check Modal authentication: modal token set")
        print(f"   3. Verify AWS credentials for S3 access")
        print(f"   4. Check Modal Volume has the model: bodypose3dnet_accuracy.onnx")
        return None


def verify_artifacts(result):
    """Verify the generated artifacts exist and are accessible"""
    
    if not result or not result.get('artifacts'):
        print("❌ No artifacts to verify")
        return False
    
    print(f"\n🔍 Verifying Generated Artifacts...")
    
    import boto3
    s3 = boto3.client('s3')
    
    verification_results = []
    
    for artifact_name, s3_url in result['artifacts'].items():
        try:
            # Parse S3 URL
            if not s3_url.startswith('s3://'):
                print(f"   ❌ {artifact_name}: Invalid S3 URL format")
                verification_results.append(False)
                continue
            
            # Extract bucket and key
            s3_path = s3_url[5:]  # Remove 's3://'
            bucket, key = s3_path.split('/', 1)
            
            # Check if object exists
            response = s3.head_object(Bucket=bucket, Key=key)
            size = response['ContentLength']
            
            print(f"   ✅ {artifact_name}: {size:,} bytes")
            verification_results.append(True)
            
        except Exception as e:
            print(f"   ❌ {artifact_name}: {e}")
            verification_results.append(False)
    
    success_rate = sum(verification_results) / len(verification_results)
    print(f"\n📈 Artifact Verification: {sum(verification_results)}/{len(verification_results)} files ({success_rate:.1%})")
    
    return all(verification_results)


def download_and_inspect_manifest(result):
    """Download and inspect the generated manifest"""
    
    if not result or 'manifest_json' not in result.get('artifacts', {}):
        print("❌ No manifest to inspect")
        return
    
    print(f"\n📋 Inspecting Generated Manifest...")
    
    try:
        import boto3
        import tempfile
        
        s3 = boto3.client('s3')
        manifest_url = result['artifacts']['manifest_json']
        
        # Parse S3 URL and download
        s3_path = manifest_url[5:]  # Remove 's3://'
        bucket, key = s3_path.split('/', 1)
        
        with tempfile.NamedTemporaryFile(mode='w+', suffix='.json', delete=False) as temp_file:
            s3.download_file(bucket, key, temp_file.name)
            
            with open(temp_file.name, 'r') as f:
                manifest = json.load(f)
            
            print(f"   ✅ Manifest downloaded and parsed")
            print(f"   📊 Key Metrics:")
            print(f"      FPS: {manifest.get('fps', 'N/A')}")
            print(f"      Resolution: {manifest.get('width', '?')}x{manifest.get('height', '?')}")
            print(f"      Orientation: {manifest.get('orientation', 'N/A')}")
            print(f"      Model Hash: {manifest.get('model_hash', 'N/A')}")
            
            if 'durations' in manifest:
                durations = manifest['durations']
                print(f"      Processing Time: {durations.get('total_sec', 'N/A')}s")
                if 'inference' in durations:
                    inf = durations['inference']
                    print(f"      Avg Inference: {inf.get('avg_ms', 'N/A')}ms")
                if 'frames_processed' in durations:
                    print(f"      Frames Processed: {durations['frames_processed']}")
            
            if 'detection' in manifest:
                det = manifest['detection']
                print(f"      Detection Stability: {det.get('smooth_stability', {}).get('stability_score', 'N/A')}")
                print(f"      Fallback Ratio: {det.get('fallback_ratio', 'N/A'):.1%}")
            
            # Clean up temp file
            os.unlink(temp_file.name)
            
    except Exception as e:
        print(f"   ❌ Manifest inspection failed: {e}")


def main():
    """Run the complete Modal processing test"""
    
    print("🎯 This test will actually process Michael_test_2.MOV through Modal")
    print("   ⚠️  Requires: Modal deployed, AWS credentials, GPU quota")
    print("")
    
    # Run the test
    result = test_modal_processing()
    
    if result:
        # Verify artifacts exist
        artifacts_ok = verify_artifacts(result)
        
        if artifacts_ok:
            # Inspect the manifest
            download_and_inspect_manifest(result)
            
            print(f"\n🎊 COMPLETE SUCCESS!")
            print(f"   ✅ Modal processing completed")
            print(f"   ✅ All artifacts generated and accessible")
            print(f"   ✅ Pipeline working end-to-end")
            print(f"\n🎬 You can now view the results:")
            print(f"   Video: {result['artifacts']['annotated_mp4']}")
            print(f"   Data: {result['artifacts']['keypoints_3d_json']}")
        else:
            print(f"\n⚠️  Processing completed but some artifacts missing")
    else:
        print(f"\n❌ Test failed - see troubleshooting steps above")


if __name__ == "__main__":
    main()