#!/usr/bin/env python3
"""
Simple Modal Test - Just call the function
"""

import modal
from uuid import uuid4

# Test payload
job_uuid = str(uuid4())
test_payload = {
    "s3_bucket": "maxwattz-videos",
    "s3_key": "maxwattz-running-videos-raw-side/Michael_test_2.MOV",
    "view": "side",
    "job_uuid": job_uuid,
    "intrinsics": {"fx": 1190.0, "fy": 1190.0, "cx": 540.0, "cy": 960.0},
    "height_cm": 178,
}

print(f"🚀 Calling Modal function with UUID: {job_uuid}")

try:
    # Call the function
    f = modal.Function.from_name("process_running_video", "run_inference")
    result = f.remote(test_payload)
    
    print(f"✅ Success! Result: {result}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()